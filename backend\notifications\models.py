from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()


class NotificationTemplate(models.Model):
    """Predefined notification templates for AI-generated content"""
    NOTIFICATION_TYPES = (
        ('WORKOUT_REMINDER', 'Workout Reminder'),
        ('MEAL_REMINDER', 'Meal Reminder'),
        ('HYDRATION_REMINDER', 'Hydration Reminder'),
        ('WEEKLY_MOTIVATION', 'Weekly Motivation'),
        ('ADMIN_MESSAGE', 'Admin Message'),
    )
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=100)
    message = models.TextField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['notification_type', 'created_at']
    
    def __str__(self):
        return f"{self.get_notification_type_display()}: {self.title}"


class UserNotificationSettings(models.Model):
    """User preferences for different notification types"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='notification_settings')
    
    # Reminder settings
    workout_reminders_enabled = models.BooleanField(default=True)
    meal_reminders_enabled = models.BooleanField(default=True)
    hydration_reminders_enabled = models.BooleanField(default=True)
    weekly_motivation_enabled = models.BooleanField(default=True)
    admin_messages_enabled = models.BooleanField(default=True)
    
    # Timing settings
    hydration_interval_hours = models.IntegerField(default=2, help_text="Hours between hydration reminders")
    workout_reminder_minutes = models.IntegerField(default=30, help_text="Minutes before workout to send reminder")
    meal_reminder_minutes = models.IntegerField(default=15, help_text="Minutes before meal to send reminder")
    
    # Weekly motivation day (0=Monday, 6=Sunday)
    weekly_motivation_day = models.IntegerField(default=1, help_text="Day of week for motivation (0=Monday)")
    weekly_motivation_hour = models.IntegerField(default=9, help_text="Hour of day for motivation (24h format)")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.user.email}'s notification settings"


class NotificationLog(models.Model):
    """Log of all notifications sent to users"""
    NOTIFICATION_TYPES = (
        ('WORKOUT_REMINDER', 'Workout Reminder'),
        ('MEAL_REMINDER', 'Meal Reminder'),
        ('HYDRATION_REMINDER', 'Hydration Reminder'),
        ('WEEKLY_MOTIVATION', 'Weekly Motivation'),
        ('ADMIN_MESSAGE', 'Admin Message'),
    )
    
    DELIVERY_STATUS = (
        ('PENDING', 'Pending'),
        ('SENT', 'Sent'),
        ('FAILED', 'Failed'),
        ('READ', 'Read'),
    )
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notification_logs')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=100)
    message = models.TextField()
    
    # Admin message specific fields
    sent_by_admin = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='sent_notifications')
    admin_message_id = models.CharField(max_length=50, null=True, blank=True, help_text="Admin's custom message ID")
    
    # Delivery tracking
    status = models.CharField(max_length=10, choices=DELIVERY_STATUS, default='PENDING')
    scheduled_for = models.DateTimeField(null=True, blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    read_at = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    metadata = models.JSONField(default=dict, blank=True, help_text="Additional data like workout_id, meal_id, etc.")
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'notification_type']),
            models.Index(fields=['status', 'scheduled_for']),
        ]
    
    def __str__(self):
        return f"{self.notification_type} for {self.user.email} - {self.status}"
    
    def mark_as_sent(self):
        self.status = 'SENT'
        self.sent_at = timezone.now()
        self.save()
    
    def mark_as_read(self):
        self.status = 'READ'
        self.read_at = timezone.now()
        self.save()


class AdminNotificationCampaign(models.Model):
    """Admin-created notification campaigns"""
    TARGET_TYPES = (
        ('ALL_USERS', 'All Users'),
        ('WORKOUT_PLAN', 'Users with Specific Workout Plan'),
        ('MEAL_PLAN', 'Users with Specific Meal Plan'),
        ('QUESTIONNAIRE', 'Users with Specific Questionnaire Answers'),
        ('CUSTOM', 'Custom User Selection'),
    )
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_campaigns')
    
    # Campaign details
    title = models.CharField(max_length=100)
    message = models.TextField()
    target_type = models.CharField(max_length=20, choices=TARGET_TYPES)
    
    # Targeting criteria
    target_workout_plan_ids = models.JSONField(default=list, blank=True)
    target_meal_plan_ids = models.JSONField(default=list, blank=True)
    target_questionnaire_criteria = models.JSONField(default=dict, blank=True)
    target_user_ids = models.JSONField(default=list, blank=True)
    
    # Campaign stats
    total_recipients = models.IntegerField(default=0)
    sent_count = models.IntegerField(default=0)
    failed_count = models.IntegerField(default=0)
    
    # Timing
    is_sent = models.BooleanField(default=False)
    sent_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Campaign: {self.title} ({self.get_target_type_display()})"
