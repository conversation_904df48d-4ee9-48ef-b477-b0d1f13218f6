import 'package:flutter/material.dart';
import 'package:provider/provider.dart'; // Import Provider
import '../utils/page_transitions.dart';
import '../utils/logger.dart'; // Import Logger
import '../screens/home/<USER>';
import '../screens/workout/workout_screen.dart';
import '../screens/workout/workout_details_screen_updated.dart';
import '../screens/workout/workout_player_screen.dart';
import '../screens/meal/meal_plan_screen.dart';
import '../screens/meal/meal_details_screen.dart';
import '../screens/shop/shop_screen.dart';
import '../screens/profile/profile_screen.dart';
import '../screens/settings/settings_screen.dart';
import '../screens/auth/signinsignup_widget.dart';
import '../screens/auth/email_verification_screen.dart';
import '../screens/auth/password_reset_screen.dart';
import '../screens/auth/verification_code_screen.dart';
import '../screens/auth/password_reset_confirm_screen.dart'; // Added this import
// Removed import for new_password_screen.dart as it was a duplicate
import '../screens/questionnaire/questionnaire_coordinator.dart';
import '../screens/questionnaire/questionnaire_processing_screen.dart'; // Import the new screen
import '../models/questionnaire_data.dart'; // Import QuestionnaireData
// Models are used in route arguments
import '../models/meal_log.dart' as meal_log_models;
import '../models/workout.dart';
import '../models/workout_log.dart';
import '../screens/main/main_screen.dart';
import '../screens/splash/splash_screen.dart';

import '../screens/welcome/welcome_screen.dart';
// Import new settings screens
import '../screens/settings/notification_settings_screen.dart';
import '../screens/notifications/notifications_screen.dart';
import '../screens/settings/personal_info_screen.dart';
import '../screens/settings/privacy_policy_screen.dart';
import '../screens/settings/about_us_screen.dart';
import '../screens/settings/help_center_screen.dart';
import '../screens/settings/submit_feedback_screen.dart';
import '../screens/settings/language_selection_screen.dart';
// Import fitness metrics screens
import '../screens/fitness/score_breakdown_screen.dart';
import '../screens/fitness/hydration_logs_screen.dart';
import '../screens/fitness/calorie_logs_screen.dart';
// Import MealCompletionScreen
import '../screens/meal/meal_completion_screen.dart'; // Add this import

import 'auth_service.dart'; // Import AuthService

class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  // Route constants
  static const String initial = '/';
  static const String splash = '/splash';
  static const String welcome = '/welcome';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String emailVerification = '/email-verification';
  static const String emailVerificationSuccess = '/email-verification/success';
  static const String emailVerificationFailure = '/email-verification/failure';
  static const String passwordReset = '/password-reset';
  static const String passwordResetConfirm = '/password-reset/confirm';
  static const String verifyEmailCode = '/verify-email-code';
  static const String verifyPasswordResetCode = '/verify-password-reset-code';
  static const String newPassword = '/password-reset/new-password';
  static const String main = '/main';
  static const String home = '/home';
  static const String workout = '/workout';
  static const String workoutDetails = '/workout-details';
  static const String workoutPlayer = '/workout-player';
  static const String mealPlan = '/meal-plan';
  static const String mealDetails = '/meal-details';
  static const String mealCompletion =
      '/meal-completion'; // Add this route constant
  static const String shop = '/shop';
  // Fitness metrics routes
  static const String scoreBreakdown = '/score-breakdown';
  static const String hydrationLogs = '/hydration-logs';
  static const String calorieLogs = '/calorie-logs';
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String notifications = '/notifications';
  // Add new settings routes
  static const String settingsNotifications = '/settings/notifications';
  static const String settingsPersonalInfo = '/settings/personal-info';
  static const String settingsPrivacyPolicy = '/settings/privacy-policy';
  static const String settingsAboutUs = '/settings/about-us';
  static const String settingsHelpCenter = '/settings/help-center';
  static const String settingsFeedback = '/settings/feedback';
  static const String settingsLanguage = '/settings/language';
  static const String settingsRetakeQuestionnaire =
      '/settings/retake-questionnaire';

  static const String questionnaire = '/questionnaire';
  // Removed temporary questionnaire route
  static const String questionnaireProcessing =
      '/questionnaire-processing'; // Add route name

  static Route<dynamic> generateRoute(RouteSettings settings) {
    try {
      // Removed log // Log route name

      if (settings.name == initial) {
        return FadePageRoute<dynamic>(page: const SplashScreen());
      } else if (settings.name == splash) {
        return FadePageRoute<dynamic>(page: const SplashScreen());
      } else if (settings.name == welcome) {
        return FadePageRoute<dynamic>(page: const WelcomeScreen());
      } else if (settings.name == login) {
        return FadePageRoute<dynamic>(
            page: const SigninsignupWidget(isSignUp: false));
      } else if (settings.name == signup) {
        return FadePageRoute<dynamic>(
            page: const SigninsignupWidget(isSignUp: true));
      } else if (settings.name == emailVerification) {
        final args = settings.arguments as Map<String, dynamic>?;
        final email = args?['email'] as String? ?? '';
        return FadePageRoute<dynamic>(
            page: EmailVerificationScreen(email: email));
      } else if (settings.name == emailVerificationSuccess) {
        return FadePageRoute<dynamic>(
            page: const EmailVerificationResultScreen(success: true));
      } else if (settings.name == emailVerificationFailure) {
        return FadePageRoute<dynamic>(
            page: const EmailVerificationResultScreen(success: false));
      } else if (settings.name == passwordReset) {
        final args = settings.arguments as Map<String, dynamic>?;
        final email = args?['email'] as String?;
        return FadePageRoute<dynamic>(page: PasswordResetScreen(email: email));
      } else if (settings.name == passwordResetConfirm) {
        final args = settings.arguments as Map<String, dynamic>?;
        final token = args?['token'] as String? ?? '';
        final uid = args?['uid'] as String? ?? '';
        return FadePageRoute<dynamic>(
            page: PasswordResetConfirmScreen(token: token, uid: uid));
      } else if (settings.name == verifyEmailCode) {
        final args = settings.arguments as Map<String, dynamic>?;
        final email = args?['email'] as String? ?? '';
        return FadePageRoute<dynamic>(
            page: VerificationCodeScreen(
          email: email,
          verificationType: VerificationType.emailVerification,
        ));
      } else if (settings.name == verifyPasswordResetCode) {
        final args = settings.arguments as Map<String, dynamic>?;
        final email = args?['email'] as String? ?? '';
        return FadePageRoute<dynamic>(
            page: VerificationCodeScreen(
          email: email,
          verificationType: VerificationType.passwordReset,
        ));
      } else if (settings.name == newPassword) {
        // Get email from arguments if available
        final args = settings.arguments as Map<String, dynamic>?;
        final email = args?['email'] as String?;
        return FadePageRoute<dynamic>(page: PasswordResetScreen(email: email));
      } else if (settings.name == main) {
        // Check for initialTabIndex in arguments
        final args = settings.arguments as Map<String, dynamic>?;
        final initialTabIndex = args?['initialTabIndex'] as int?;
        return SlidePageRoute<dynamic>(
            page: MainScreen(initialTabIndex: initialTabIndex));
      } else if (settings.name == home) {
        return SlidePageRoute<dynamic>(page: const HomeScreen());
      } else if (settings.name == workout) {
        return SlidePageRoute<dynamic>(page: const WorkoutScreen());
      } else if (settings.name == workoutDetails) {
        final args = settings.arguments as Map<String, dynamic>?;

        // Create a workout object from the map
        Map<String, dynamic> workoutMap;
        try {
          if (args?['workout'] is Map<String, dynamic>) {
            // If it's a Map, use it directly
            workoutMap = args!['workout'] as Map<String, dynamic>;
          } else {
            // Fallback to a default workout map
            workoutMap = {
              'id': 0,
              'name': 'Workout',
              'description': 'No description available',
              'isRestDay': false,
            };
          }
        } catch (e) {
          // Error creating workout map
          // Fallback to a default workout map
          workoutMap = {
            'id': 0,
            'name': 'Workout',
            'description': 'Error loading workout details',
            'isRestDay': false,
          };
        }

        // Extract workoutLogs list
        List<WorkoutLog> workoutLogsList = [];
        try {
          if (args?['workoutLogs'] is List) {
            // Attempt to cast each item, handling potential type errors
            workoutLogsList = (args!['workoutLogs'] as List)
                .map((item) {
                  if (item is WorkoutLog) {
                    return item;
                  } else if (item is Map<String, dynamic>) {
                    // If it's a map, try to deserialize (might need more robust error handling)
                    try {
                      return WorkoutLog.fromJson(item);
                    } catch (e) {
                      Logger.error(
                          'Error deserializing WorkoutLog from map in navigation: $e',
                          tag: 'NavigationService');
                      return null; // Return null for items that fail deserialization
                    }
                  } else {
                    Logger.error(
                        'Unexpected item type in workoutLogs list: ${item.runtimeType}',
                        tag: 'NavigationService');
                    return null; // Return null for unexpected types
                  }
                })
                .whereType<WorkoutLog>()
                .toList(); // Filter out nulls
          }
          if (workoutLogsList.isEmpty && args?['workoutLog'] is WorkoutLog) {
            // Fallback: If workoutLogs is empty/missing, but a single workoutLog exists, wrap it in a list
            Logger.warning(
                'workoutLogs missing or empty, using single workoutLog fallback.',
                tag: 'NavigationService');
            workoutLogsList = [args!['workoutLog'] as WorkoutLog];
          }
        } catch (e) {
          Logger.error('Error processing workoutLogs argument: $e',
              tag: 'NavigationService');
          // workoutLogsList will remain empty, potentially leading to an error screen below
        }

        // Check if workoutLogsList is empty after processing
        if (workoutLogsList.isEmpty) {
          Logger.error('workoutLogs list is empty after processing arguments.',
              tag: 'NavigationService');
          return MaterialPageRoute(
              builder: (_) => Scaffold(
                  appBar: AppBar(title: const Text('Error')),
                  body: const Center(
                      child: Text(
                          'Error: Missing or invalid workout log data.'))));
        }

        // Extract targetSessionId if provided
        int? targetSessionId;
        try {
          Logger.workout(
              'Processing workout details arguments: ${args?.keys.join(', ')}',
              tag: 'NavigationService');

          if (args?['targetSessionId'] is int) {
            targetSessionId = args!['targetSessionId'] as int;
            Logger.workout('Found targetSessionId: $targetSessionId',
                tag: 'NavigationService');
          } else {
            Logger.workout(
                'No targetSessionId found in arguments or not an int: ${args?['targetSessionId']}',
                tag: 'NavigationService');
          }

          // Log details of workoutLogs for debugging
          if (workoutLogsList.isNotEmpty) {
            Logger.workout('WorkoutLogs details:', tag: 'NavigationService');
            for (int i = 0; i < workoutLogsList.length; i++) {
              final log = workoutLogsList[i];
              Logger.workout(
                  '  Log[$i]: id=${log.id}, workoutDay=${log.workoutDay?.id}, workoutSession=${log.workoutSession}, isCompleted=${log.isCompleted}',
                  tag: 'NavigationService');

              // Log details of workoutDay sessions if available
              if (log.workoutDay?.sessions != null) {
                final sessions = log.workoutDay!.sessions!;
                Logger.workout('    WorkoutDay sessions: ${sessions.length}',
                    tag: 'NavigationService');
                for (int j = 0; j < sessions.length; j++) {
                  final session = sessions[j];
                  Logger.workout(
                      '      Session[$j]: id=${session.id}, name="${session.name}", sections=${session.sections?.length ?? 0}',
                      tag: 'NavigationService');
                }
              }
            }
          }
        } catch (e) {
          Logger.error('Error extracting targetSessionId: $e',
              tag: 'NavigationService');
        }

        return SlidePageRoute<dynamic>(
          page: WorkoutDetailsScreen(
            workout: workoutMap, // Pass the workout map
            workoutLogs:
                workoutLogsList, // Pass the processed list of WorkoutLog objects
            targetSessionId:
                targetSessionId, // Pass the target session ID if provided
          ),
        );
      } else if (settings.name == workoutPlayer) {
        final args = settings.arguments as Map<String, dynamic>?;
        if (args == null) {
          // Handle error: Arguments are missing
          return _errorRoute(settings.name ?? 'unknown route',
              'Missing arguments for WorkoutPlayerScreen');
        }
        final workoutLog = args['workoutLog'] as WorkoutLog?;
        final workoutSessionLogId = args['workoutSessionLogId'] as int?;
        final startIndex = args['startIndex'] as int? ?? 0;
        final allExercises = args['allExercises'] as List<WorkoutExerciseLog>?;

        if (workoutLog == null || workoutSessionLogId == null) {
          // Handle error: Required arguments are missing or invalid
          return _errorRoute(settings.name ?? 'unknown route',
              'Missing required workoutLog or workoutSessionLogId for WorkoutPlayerScreen');
        }

        return MaterialPageRoute(
          builder: (context) => WorkoutPlayerScreen(
            workoutLog: workoutLog,
            workoutSessionLogId: workoutSessionLogId,
            startIndex: startIndex,
            allExercises: allExercises,
          ),
        );
      } else if (settings.name == mealPlan) {
        return SlidePageRoute<dynamic>(page: const MealPlanScreen());
      } else if (settings.name == shop) {
        return SlidePageRoute<dynamic>(page: const ShopScreen());
      } else if (settings.name == mealDetails) {
        // Arguments should be a Map<String, dynamic> containing MealLog and optionally date
        final dynamic routeArgs =
            settings.arguments; // Get arguments as dynamic first

        if (routeArgs is Map<String, dynamic>) {
          // Check if it's a Map
          final mealLogArg = routeArgs['mealLog'];
          // Extract date if needed in the future
          // final dateArg = routeArgs['date']; // Can be null or String

          // Validate the mealLog argument specifically
          if (mealLogArg is meal_log_models.MealLog) {
            // Arguments are valid, pass the original map to the screen
            return SlidePageRoute<dynamic>(
              page: MealDetailsScreen(arguments: routeArgs),
            );
          } else {
            // mealLog key exists but value is not a MealLog object
            return _errorRoute(settings.name ?? 'unknown route',
                'Invalid mealLog data type in arguments.');
          }
        } else {
          // Arguments are not a Map or are null
          return _errorRoute(settings.name ?? 'unknown route',
              'Missing or invalid arguments for meal details.');
        }
      } else if (settings.name == mealCompletion) {
        // Add this block
        final args = settings.arguments as Map<String, dynamic>?;
        if (args == null || args['mealLog'] is! meal_log_models.MealLog) {
          // Handle error: Missing or invalid mealLog argument
          return _errorRoute(settings.name ?? 'unknown route',
              'Missing or invalid mealLog for MealCompletionScreen');
        }
        final mealLog = args['mealLog'] as meal_log_models.MealLog;
        return MaterialPageRoute(
          // Or use your custom SlidePageRoute if preferred
          builder: (context) => MealCompletionScreen(mealLog: mealLog),
        );
      } else if (settings.name == profile) {
        return SlidePageRoute<dynamic>(page: const ProfileScreen());
      } else if (settings.name == notifications) {
        return SlidePageRoute<dynamic>(page: const NotificationsScreen());
      } else if (settings.name == NavigationService.settings) {
        // Explicitly use class name
        // Use SlidePageRoute for consistency
        return SlidePageRoute<dynamic>(page: const SettingsScreen());
      }
      // Add cases for new settings routes
      else if (settings.name == settingsNotifications) {
        return SlidePageRoute<dynamic>(
            page: const NotificationSettingsScreen());
      } else if (settings.name == settingsPersonalInfo) {
        return SlidePageRoute<dynamic>(page: const PersonalInfoScreen());
      } else if (settings.name == settingsPrivacyPolicy) {
        return SlidePageRoute<dynamic>(page: const PrivacyPolicyScreen());
      } else if (settings.name == settingsAboutUs) {
        return SlidePageRoute<dynamic>(page: const AboutUsScreen());
      } else if (settings.name == settingsHelpCenter) {
        return SlidePageRoute<dynamic>(page: const HelpCenterScreen());
      } else if (settings.name == settingsFeedback) {
        return SlidePageRoute<dynamic>(page: const SubmitFeedbackScreen());
      } else if (settings.name == settingsLanguage) {
        return SlidePageRoute<dynamic>(page: const LanguageSelectionScreen());
      }
      // Add cases for fitness metrics routes
      else if (settings.name == scoreBreakdown) {
        return SlidePageRoute<dynamic>(page: const ScoreBreakdownScreen());
      } else if (settings.name == hydrationLogs) {
        return SlidePageRoute<dynamic>(page: const HydrationLogsScreen());
      } else if (settings.name == calorieLogs) {
        return SlidePageRoute<dynamic>(page: const CalorieLogsScreen());
      } else if (settings.name == questionnaire) {
        // onComplete now calls AuthService to submit data and then navigates
        return SlidePageRoute<void>(
          // Doesn't need to return data anymore
          page: QuestionnaireCoordinator(
            onComplete: (QuestionnaireData data) async {
              // Make async
              final context = navigatorKey.currentContext;
              if (context == null) {
                // Removed log
                // Navigate to main even if context fails, maybe show error there
                navigatorKey.currentState?.pushReplacementNamed(main);
                return;
              }
              // Show loading indicator or disable interaction if needed here
              // Consider showing a loading dialog: showDialog(context: context, builder: (_) => Center(child: CircularProgressIndicator()));
              try {
                final authService =
                    Provider.of<AuthService>(context, listen: false);
                // Removed log
                await authService.submitQuestionnaire(data);
                // Removed log
                // Always navigate to processing screen after submission
                // Pop loading dialog if shown: Navigator.of(context).pop();
                navigatorKey.currentState
                    ?.pushReplacementNamed(questionnaireProcessing);
              } catch (e) {
                // Removed log
                // Pop loading dialog if shown: Navigator.of(context).pop();
                // Optionally show an error message here using the valid context
                // ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Failed to submit questionnaire: $e')));
                // Navigate to the processing screen instead of main
                navigatorKey.currentState
                    ?.pushReplacementNamed(questionnaireProcessing);
              }
            },
          ),
        );
      } else if (settings.name == questionnaireProcessing) {
        // Add route definition
        return FadePageRoute<dynamic>(
            page: const QuestionnaireProcessingScreen());
      } else {
        // Log the specific route name that wasn't matched
        // Removed log
        return FadePageRoute<dynamic>(
          page: Scaffold(
            body: Center(
              child: Text('No route defined for ${settings.name}'),
            ),
          ),
        );
      }
    } catch (e) {
      // Removed log
      return MaterialPageRoute(
        builder: (_) => Scaffold(
          body: Center(
            child: Text('Error loading route: $e'),
          ),
        ),
      );
    }
  }

  static Future<T?> navigateTo<T>(Widget page) {
    if (navigatorKey.currentState == null) {
      throw Exception('Navigator key is null');
    }
    return navigatorKey.currentState!.push<T>(
      SlidePageRoute<T>(page: page),
    );
  }

  static Future<T?> navigateToReplacement<T>(Widget page) {
    // Removed log
    if (navigatorKey.currentState == null) {
      // Removed log
      throw Exception('Navigator key is null');
    }
    try {
      // Removed log
      return navigatorKey.currentState!.pushReplacement<T, void>(
        SlidePageRoute<T>(page: page),
      );
    } catch (e) {
      // Removed log
      rethrow;
    }
  }

  static Future<T?> navigateWithFade<T>(Widget page) {
    if (navigatorKey.currentState == null) {
      throw Exception('Navigator key is null');
    }
    return navigatorKey.currentState!.push<T>(
      FadePageRoute<T>(page: page),
    );
  }

  static void goBack() {
    if (navigatorKey.currentState == null) {
      throw Exception('Navigator key is null');
    }

    // Get the current route name
    final currentRoute =
        ModalRoute.of(navigatorKey.currentContext!)?.settings.name;

    // Log the current route for debugging
    Logger.log('Current route: $currentRoute', tag: 'NavigationService.goBack');

    // If we can pop, do so
    if (navigatorKey.currentState!.canPop()) {
      navigatorKey.currentState!.pop();
    } else {
      // If we can't pop, always go to main screen
      // This prevents going back to welcome/login screens after authentication
      navigatorKey.currentState!.pushReplacementNamed(main);
    }
  }

  static Future<T?> navigateToNamed<T>(String routeName, {Object? arguments}) {
    if (navigatorKey.currentState == null) {
      throw Exception('Navigator key is null');
    }
    return navigatorKey.currentState!
        .pushNamed<T>(routeName, arguments: arguments);
  }

  static Future<T?> navigateToReplacementNamed<T>(String routeName,
      {Object? arguments}) {
    if (navigatorKey.currentState == null) {
      throw Exception('Navigator key is null');
    }
    return navigatorKey.currentState!
        .pushReplacementNamed(routeName, arguments: arguments);
  }

  static Route<dynamic> _errorRoute(String routeName, String errorMessage) {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        body: Center(
          child: Text('Error: $errorMessage'),
        ),
      ),
    );
  }
}
