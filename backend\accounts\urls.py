from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views
from .views import (
    UserViewSet, UserProfileViewSet,
    UserScoreBreakdownView, UserScoreHistoryView,
    UserScorePercentileView, UserProgressView,
    # Removed UserStreakView
    CSRFTokenView, LoginView,
    UserScoreAPIView, UserProgressAPIView, UserProfileAPIView,
    get_user_calories, update_user_weight
)
# Import auth views and JWT views needed for auth paths
from .auth_views import (
    register, LoginView, LogoutView # Removed verify_email, resend_verification, TokenView
)
from .auth_jwt import CustomTokenRefreshView
from rest_framework_simplejwt.views import TokenVerifyView
# Import verification views
from .verification_views import (
    SendVerificationCodeView, VerifyEmailCodeView,
    VerifyPasswordResetCodeView, ResetPasswordWithCodeView,
    GetLatestVerificationCodeView
)


router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'profiles', UserProfileViewSet)

urlpatterns = [
    path('', include(router.urls)), # Router handles /profiles/ and /profiles/me/
    # path('users/profile/', UserProfileViewSet.as_view({'get': 'me'}), name='user-profile'), # Removed - Handled by router
    path('users/profile/update/', UserProfileAPIView.as_view(), name='update-profile'), # Use UserProfileAPIView instead of ViewSet
    path('users/profile/questionnaire/', UserProfileViewSet.as_view({'post': 'questionnaire'}), name='user-questionnaire'), # Keep specific non-router actions if needed, or move to ViewSet actions

    # Score and progress endpoints
    # path('users/score/api/', UserScoreAPIView.as_view(), name='user-score-api'), # Removed duplicate
    path('users/progress/api/', UserProgressAPIView.as_view(), name='user-progress-api'), # Keeping API version for progress

    # Use UserScoreAPIView for the main score endpoint
    path('users/score/', UserScoreAPIView.as_view(), name='user-score'), # Reverted to original path
    # path('score-debug/', UserScoreAPIView.as_view(), name='user-score-debug'), # Removed temporary path
    path('users/score/breakdown/', UserScoreBreakdownView.as_view(), name='score-breakdown'),
    path('users/score/history/', UserScoreHistoryView.as_view(), name='score-history'),
    path('users/score/percentile/', UserScorePercentileView.as_view(), name='score-percentile'),
    path('users/progress/', UserProgressView.as_view(), name='user-progress'),
    # Removed path for UserStreakView
    path('users/calories/', get_user_calories, name='user-calories'),
    path('users/weight/update/', update_user_weight, name='update-weight'),

    # Authentication URLs (moved from main urls.py)
    path('auth/register/', register, name='account-register'), # Renamed for clarity
    # path('auth/verify-email/<str:uidb64>/<str:token>/', verify_email, name='account-verify-email'), # Commented out
    # path('auth/resend-verification/', resend_verification, name='account-resend-verification'), # Commented out
    path('auth/login/', LoginView.as_view(), name='account-login'), # Renamed
    path('auth/logout/', LogoutView.as_view(), name='account-logout'), # Renamed
    # path('auth/token/', TokenView.as_view(), name='account-token'), # Removed - TokenView deleted
    path('auth/token/refresh/', CustomTokenRefreshView.as_view(), name='account-token-refresh'), # Renamed
    path('auth/token/verify/', TokenVerifyView.as_view(), name='account-token-verify'), # Renamed
    path('auth/csrf-token/', CSRFTokenView.as_view(), name='account-csrf-token'), # Renamed

    # Verification code endpoints
    path('auth/send-verification-code/', SendVerificationCodeView.as_view(), name='send-verification-code'),
    path('auth/verify-email-code/', VerifyEmailCodeView.as_view(), name='verify-email-code'),
    path('auth/password/reset/verify-code/', VerifyPasswordResetCodeView.as_view(), name='verify-password-reset-code'),
    path('auth/password/reset/confirm-code/', ResetPasswordWithCodeView.as_view(), name='reset-password-with-code'),
    path('auth/get-latest-verification-code/', GetLatestVerificationCodeView.as_view(), name='get-latest-verification-code'),
]
