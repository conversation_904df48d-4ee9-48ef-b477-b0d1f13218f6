import 'package:workmanager/workmanager.dart';
import 'notification_api_service.dart';
import 'local_notification_service.dart';
import '../utils/logger.dart';

class BackgroundNotificationService {
  static const String _syncTaskName = 'notification_sync_task';
  static const String _hydrationReminderTask = 'hydration_reminder_task';
  
  /// Initialize the background service
  static Future<void> initialize() async {
    try {
      await Workmanager().initialize(
        callbackDispatcher,
        isInDebugMode: false, // Set to false in production
      );
      
      Logger.log('Background notification service initialized', tag: 'BackgroundNotificationService');
    } catch (e) {
      Logger.log('Error initializing background service: $e', tag: 'BackgroundNotificationService');
    }
  }

  /// Schedule periodic notification sync
  static Future<void> scheduleNotificationSync() async {
    try {
      await Workmanager().registerPeriodicTask(
        _syncTaskName,
        _syncTaskName,
        frequency: const Duration(hours: 1), // Sync every hour
        constraints: Constraints(
          networkType: NetworkType.connected,
        ),
      );
      
      Logger.log('Notification sync task scheduled', tag: 'BackgroundNotificationService');
    } catch (e) {
      Logger.log('Error scheduling notification sync: $e', tag: 'BackgroundNotificationService');
    }
  }

  /// Schedule hydration reminders
  static Future<void> scheduleHydrationReminders() async {
    try {
      await Workmanager().registerPeriodicTask(
        _hydrationReminderTask,
        _hydrationReminderTask,
        frequency: const Duration(hours: 2), // Every 2 hours
      );
      
      Logger.log('Hydration reminder task scheduled', tag: 'BackgroundNotificationService');
    } catch (e) {
      Logger.log('Error scheduling hydration reminders: $e', tag: 'BackgroundNotificationService');
    }
  }

  /// Cancel all background tasks
  static Future<void> cancelAllTasks() async {
    try {
      await Workmanager().cancelAll();
      Logger.log('All background tasks cancelled', tag: 'BackgroundNotificationService');
    } catch (e) {
      Logger.log('Error cancelling background tasks: $e', tag: 'BackgroundNotificationService');
    }
  }

  /// Cancel specific task
  static Future<void> cancelTask(String taskName) async {
    try {
      await Workmanager().cancelByUniqueName(taskName);
      Logger.log('Task $taskName cancelled', tag: 'BackgroundNotificationService');
    } catch (e) {
      Logger.log('Error cancelling task $taskName: $e', tag: 'BackgroundNotificationService');
    }
  }
}

/// Background task callback dispatcher
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      Logger.log('Executing background task: $task', tag: 'BackgroundTask');
      
      switch (task) {
        case BackgroundNotificationService._syncTaskName:
          await _syncNotifications();
          break;
          
        case BackgroundNotificationService._hydrationReminderTask:
          await _showHydrationReminder();
          break;
          
        default:
          Logger.log('Unknown background task: $task', tag: 'BackgroundTask');
          return false;
      }
      
      Logger.log('Background task $task completed successfully', tag: 'BackgroundTask');
      return true;
      
    } catch (e) {
      Logger.log('Error executing background task $task: $e', tag: 'BackgroundTask');
      return false;
    }
  });
}

/// Sync notifications from server and show local notifications
Future<void> _syncNotifications() async {
  try {
    // Get recent notifications from server
    final notifications = await NotificationApiService.getNotificationLogs(limit: 5);
    
    // Show unread notifications as local notifications
    for (final notification in notifications) {
      if (notification.isUnread) {
        await LocalNotificationService.showGeneralNotification(
          title: notification.title,
          message: notification.message,
          type: notification.notificationType,
        );
      }
    }
    
    Logger.log('Synced ${notifications.length} notifications', tag: 'BackgroundSync');
    
  } catch (e) {
    Logger.log('Error syncing notifications: $e', tag: 'BackgroundSync');
  }
}

/// Show hydration reminder
Future<void> _showHydrationReminder() async {
  try {
    // Get user's notification settings to check if hydration reminders are enabled
    final settings = await NotificationApiService.getNotificationSettings();
    
    if (settings?.hydrationRemindersEnabled == true) {
      await LocalNotificationService.showGeneralNotification(
        title: '💧 Hydration Time!',
        message: 'Time for a refreshing glass of water. Stay hydrated!',
        type: 'HYDRATION_REMINDER',
      );
      
      Logger.log('Hydration reminder shown', tag: 'BackgroundHydration');
    } else {
      Logger.log('Hydration reminders disabled, skipping', tag: 'BackgroundHydration');
    }
    
  } catch (e) {
    Logger.log('Error showing hydration reminder: $e', tag: 'BackgroundHydration');
  }
}
