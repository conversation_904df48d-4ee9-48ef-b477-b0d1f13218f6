import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:image_picker/image_picker.dart';
import 'package:dio/dio.dart'; // Needed for picture upload
import 'package:timezone/timezone.dart' as tz;

// Keeping your existing imports
import '../../services/api_service.dart';
import '../../models/user_profile.dart';
import '../../models/user_score.dart';
import '../../services/auth_service.dart'; // For AuthService
import '../../constants/api_constants.dart'; // Needed for picture upload
import '../../utils/logger.dart'; // For logging
import 'package:intl/intl.dart'; // For date formatting
import '../../utils/date_utils.dart' as app_date_utils; // For timezone handling

// --- Placeholder models (REMOVE IF YOU HAVE REAL IMPORTS) ---
// class UserProfile { final String? profile_picture; final int? age; final double? weight; UserProfile({this.profile_picture, this.age, this.weight}); factory UserProfile.fromJson(Map<String,dynamic> json) => UserProfile();}
// class UserScore { final int? totalScore; UserScore({this.totalScore}); }
// class User { final String? firstName; final String? username; User({this.firstName, this.username}); }
// --- END Placeholders ---

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with WidgetsBindingObserver {
  final ApiService _apiService = ApiService();
  UserProfile? _userProfile;
  UserScore? _userScore;
  Map<String, dynamic>? _calorieData;
  List<FlSpot> _chartData = [];
  List<String> _chartDayLabels = [];
  bool _isLoading = true;
  String? _error;
  // Removed unused scroll controller
  // No longer need AuthService field as we use Provider directly

  // Raw profile data for debugging and fallback
  Map<String, dynamic>? _rawUserProfileData;

  @override
  void initState() {
    super.initState();
    // Register this object as an observer to detect when app resumes
    WidgetsBinding.instance.addObserver(this);
    // Load profile data when screen initializes
    _loadProfileData();
  }

  @override
  void dispose() {
    // Remove observer when widget is disposed
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Refresh data when app resumes
    if (state == AppLifecycleState.resumed) {
      _loadProfileData();
    }
  }

  // This method will be called when this screen is navigated to
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh data when screen becomes visible
    _loadProfileData();
  }

  Future<void> _loadProfileData({bool skipCache = false}) async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Log API calls starting
      Logger.log('Starting API calls', tag: 'ProfileScreen');

      // Get the AuthService and check auth status
      final authService = Provider.of<AuthService>(context, listen: false);
      Logger.log('Current user: ${authService.currentUser}',
          tag: 'ProfileScreen');

      // First get user profile since we need that most
      try {
        final response = await _apiService.getUserProfile(skipCache: skipCache);
        Logger.log('Raw UserProfile response: $response', tag: 'ProfileScreen');

        // Assuming getUserProfile() always returns a UserProfile object
        _userProfile = response;

        // Create a debugging representation
        _rawUserProfileData = {
          'profile_picture': _userProfile!.profile_picture,
          'age': _userProfile!.age,
          'weight': _userProfile!.weight,
          'user': _userProfile!.user,
          'direct_object': true
        };
        Logger.log('Received UserProfile object', tag: 'ProfileScreen');
      } catch (e) {
        Logger.log('getUserProfile error: $e', tag: 'ProfileScreen');
        _rawUserProfileData = {'error': e.toString()};
      }

      // Then get user score
      try {
        final score = await _apiService.getUserScore();
        Logger.log('Raw UserScore response: $score', tag: 'ProfileScreen');

        // Store the UserScore object
        _userScore = score;

        Logger.log('Processed UserScore', tag: 'ProfileScreen');
      } catch (e) {
        Logger.log('getUserScore error: $e', tag: 'ProfileScreen');
      }

      // Get calories data
      try {
        _calorieData = await _apiService.getCalories();
        Logger.log('CalorieData: $_calorieData', tag: 'ProfileScreen');
      } catch (e) {
        Logger.log('getCalories error: $e', tag: 'ProfileScreen');
        _calorieData = {};
      }

      // Get score history
      Map<String, dynamic> scoreHistory = {};
      try {
        scoreHistory = await _apiService.getUserScoreHistory();
        Logger.log('ScoreHistory fetched successfully', tag: 'ProfileScreen');
      } catch (e) {
        Logger.log('getUserScoreHistory error: $e', tag: 'ProfileScreen');
      }

      if (mounted) {
        setState(() {
          _chartData = _processScoreHistoryData(scoreHistory);
          _isLoading = false;
        });
      }
    } catch (e) {
      Logger.log('Error loading data: $e', tag: 'ProfileScreen');
      if (mounted) {
        setState(() {
          _error = 'Failed to load profile data: $e';
          _isLoading = false;
        });
      }
    }
  }

  List<FlSpot> _processScoreHistoryData(Map<String, dynamic>? scoreHistory) {
    final currentScore =
        _userScore?.totalScore?.toDouble() ?? 0.0; // Default to 0 if no score

    // Clear the day labels list
    _chartDayLabels = [];

    // Get user's timezone from auth service
    final authService = Provider.of<AuthService>(context, listen: false);
    String? userTimezone =
        authService.currentUser?.profile?['timezone'] as String?;

    // Log the timezone being used
    Logger.log('Using timezone: $userTimezone for chart data',
        tag: 'ProfileScreen');

    // If timezone is null or empty, try to use a more reliable timezone
    if (userTimezone == null || userTimezone.isEmpty) {
      // Use Asia/Calcutta as a fallback, similar to other screens in the app
      userTimezone = 'Asia/Calcutta';
      Logger.log(
          'Timezone not found in user profile, using fallback: $userTimezone',
          tag: 'ProfileScreen');
    }

    final location = app_date_utils.getLocation(userTimezone);

    // Log the score history data for debugging
    Logger.log('Score history data: $scoreHistory', tag: 'ProfileScreen');

    // Check if we have valid score history data
    if (scoreHistory == null || scoreHistory.isEmpty) {
      // If no data, create a sample week with current score
      _chartDayLabels.clear();
      List<FlSpot> spots = [];
      final now = DateTime.now();

      for (int i = 6; i >= 0; i--) {
        final date = now.subtract(Duration(days: i));
        String dayLabel;
        if (i == 0) {
          dayLabel = 'Today';
        } else if (i == 1) {
          dayLabel = 'Yesterday';
        } else {
          dayLabel = DateFormat('E').format(date);
        }
        _chartDayLabels.add(dayLabel);
        // Show gradual progress towards current score
        double score = currentScore * (1.0 - (i * 0.1));
        if (score < 0) score = 0;
        spots.add(FlSpot((6 - i).toDouble(), score));
      }
      return spots;
    }

    // Check if we have score_history in the response
    if (scoreHistory.containsKey('score_history') &&
        scoreHistory['score_history'] is List) {
      // Get the score history list
      final historyList = scoreHistory['score_history'] as List;

      // If the list is empty, just show the current score
      if (historyList.isEmpty) {
        _chartDayLabels.add('Today');
        return [FlSpot(0, currentScore)];
      }

      // Convert the history data to a list of maps
      final entries = List<Map<String, dynamic>>.from(
          historyList.map((item) => item as Map<String, dynamic>));

      // Sort entries by date
      entries
          .sort((a, b) => (a['date'] as String).compareTo(b['date'] as String));

      // We'll use tz.TZDateTime.now(location) directly when needed

      // Convert the entries to FlSpot points
      List<FlSpot> spots = [];
      for (int i = 0; i < entries.length; i++) {
        final entry = entries[i];
        final scoreValue = entry['score'];
        final score = (scoreValue is int)
            ? scoreValue.toDouble()
            : (scoreValue is double)
                ? scoreValue
                : 0.0;
        spots.add(FlSpot(i.toDouble(), score));

        // Add the day label with date formatting
        final dateStr = entry['date'] as String;
        final date = DateTime.parse(dateStr);

        // Convert to user's local timezone
        final localDate = tz.TZDateTime.from(date, location);

        // Get current time in user's timezone
        final now = tz.TZDateTime.now(location);

        // Format the date label based on whether it's today, yesterday, or another day
        String dayLabel;
        if (localDate.year == now.year &&
            localDate.month == now.month &&
            localDate.day == now.day) {
          dayLabel = 'Today';
        } else if (localDate.year == now.year &&
            localDate.month == now.month &&
            localDate.day == now.day - 1) {
          dayLabel = 'Yesterday';
        } else {
          // Use short day name without newline to prevent clipping
          dayLabel = DateFormat('E d').format(localDate);
        }

        _chartDayLabels.add(dayLabel);

        // Log the date conversion for debugging
        Logger.log(
            'Date conversion: API date=$dateStr, localDate=$localDate, label=$dayLabel',
            tag: 'ProfileScreen');
      }

      Logger.log('Created ${spots.length} chart points from score history',
          tag: 'ProfileScreen');
      return spots;
    } else {
      // If we don't have score_history, just show the current score
      _chartDayLabels.add('Today');
      return [FlSpot(0, currentScore)];
    }
  }

  Future<void> _editProfilePicture() async {
    try {
      final ImagePicker picker = ImagePicker();
      final source = await showDialog<ImageSource>(
        context: context,
        builder: (BuildContext context) {
          return SimpleDialog(
            title: const Text('Select Image Source'),
            children: <Widget>[
              SimpleDialogOption(
                onPressed: () => Navigator.pop(context, ImageSource.camera),
                child: const Row(children: [
                  Icon(Icons.camera_alt_outlined),
                  SizedBox(width: 8),
                  Text('Camera')
                ]),
              ),
              SimpleDialogOption(
                onPressed: () => Navigator.pop(context, ImageSource.gallery),
                child: const Row(children: [
                  Icon(Icons.photo_library_outlined),
                  SizedBox(width: 8),
                  Text('Gallery')
                ]),
              ),
              SimpleDialogOption(
                onPressed: () => Navigator.pop(context),
                child:
                    const Text('Cancel', style: TextStyle(color: Colors.grey)),
              ),
            ],
          );
        },
      );

      if (source == null || !mounted) return;

      final XFile? image =
          await picker.pickImage(source: source, imageQuality: 70);
      if (image == null) return;

      final File imageFile = File(image.path);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Row(children: [
                CircularProgressIndicator(strokeWidth: 3),
                SizedBox(width: 16),
                Text('Uploading picture...')
              ]),
              duration: Duration(minutes: 1)),
        );
      }

      final String fileName = imageFile.path.split('/').last;
      final formData = FormData.fromMap({
        'profile_picture':
            await MultipartFile.fromFile(imageFile.path, filename: fileName),
      });

      // Use the updateUserProfile method directly since we know it handles the correct endpoint
      try {
        await _apiService.updateUserProfile({'profile_picture': formData});

        // Force refresh the profile data to get the updated profile picture
        await _loadProfileData(skipCache: true);

        if (mounted) {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Profile picture updated!'),
                backgroundColor: Colors.black87),
          );
        }
      } catch (uploadError) {
        // If updateUserProfile fails, try using the patch method directly
        Logger.log('Error using updateUserProfile: $uploadError',
            tag: 'ProfileScreen');

        final response = await _apiService.patch(
          ApiConstants.userProfileUpdate,
          formData: formData,
        );

        if (!mounted) return;
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        if (response != null) {
          // Force refresh the profile data to get the updated profile picture
          await _loadProfileData(skipCache: true);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                  content: Text('Profile picture updated!'),
                  backgroundColor: Colors.black87),
            );
          }
        } else {
          throw Exception("Upload failed - API returned null");
        }
      }
    } catch (e) {
      Logger.log('Error uploading picture: $e', tag: 'ProfileScreen');
      if (!mounted) return;
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Error updating picture: ${e.toString()}'),
            backgroundColor: Colors.black87),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(
            color: Color(0xFFFF7F36), // Orange color to match app theme
          ),
        ),
      );
    }

    // If we have an error, show it with a design matching the home screen
    if (_error != null) {
      return Scaffold(
        backgroundColor: const Color(0xFF101114),
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header with background image
              Stack(
                children: [
                  ShaderMask(
                    shaderCallback: (rect) {
                      return LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withValues(
                              red: 0, green: 0, blue: 0, alpha: 179),
                          Colors.black
                              .withValues(red: 0, green: 0, blue: 0, alpha: 230)
                        ],
                      ).createShader(rect);
                    },
                    blendMode: BlendMode.darken,
                    child: Image.asset(
                      'assets/images/homebg.png',
                      height: 200,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Positioned(
                    top: 16,
                    right: 16,
                    child: IconButton(
                      icon: const Icon(Icons.refresh, color: Colors.white),
                      onPressed: _loadProfileData,
                      tooltip: 'Refresh',
                    ),
                  ),
                  const Positioned(
                    bottom: 24,
                    left: 24,
                    child: Text(
                      'Profile',
                      style: TextStyle(
                        color: Colors.white,
                        fontFamily: 'Work Sans',
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              // Error content
              Expanded(
                child: Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                  ),
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Color(0xFFFF7F36),
                          size: 64,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Unable to load profile',
                          style: TextStyle(
                            color: Color(0xFF101114),
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Work Sans',
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Pull down to refresh',
                          style: TextStyle(
                            color: Colors.black54,
                            fontSize: 16,
                            fontFamily: 'Work Sans',
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Get profile image URL with fallback
    String? profileImageUrl;
    if (_userProfile != null &&
        _userProfile!.profile_picture != null &&
        _userProfile!.profile_picture!.isNotEmpty) {
      profileImageUrl = _userProfile!.profile_picture;
    } else if (_rawUserProfileData != null) {
      // Check common profile image field names
      if (_rawUserProfileData!.containsKey('profile_picture') &&
          _rawUserProfileData!['profile_picture'] is String) {
        profileImageUrl = _rawUserProfileData!['profile_picture'] as String;
      } else if (_rawUserProfileData!.containsKey('avatar') &&
          _rawUserProfileData!['avatar'] is String) {
        profileImageUrl = _rawUserProfileData!['avatar'] as String;
      } else if (_rawUserProfileData!.containsKey('image') &&
          _rawUserProfileData!['image'] is String) {
        profileImageUrl = _rawUserProfileData!['image'] as String;
      }
    }

    // Get the auth service for fallback username
    final authService = Provider.of<AuthService>(context, listen: false);

    // Try to get a reasonable display name
    String displayName = "User";

    // First check auth service (this is likely to have the user's name)
    if (authService.currentUser != null) {
      if (authService.currentUser!.firstName.isNotEmpty) {
        displayName = authService.currentUser!.firstName;
      } else if (authService.currentUser!.username.isNotEmpty) {
        displayName = authService.currentUser!.username;
      } else if (authService.currentUser!.email.isNotEmpty) {
        // Use email but remove domain part
        final emailParts = authService.currentUser!.email.split('@');
        if (emailParts.isNotEmpty) {
          displayName = emailParts[0];
        }
      }
    }

    // Then check UserProfile (if that has better data)
    if (_userProfile != null) {
      // Try different possible profile fields - different APIs might use different field names
      if (_userProfile!.user.isNotEmpty && !_userProfile!.user.contains('-')) {
        displayName = _userProfile!.user;
      }

      // Check if raw data has a name field
      if (_rawUserProfileData != null) {
        if (_rawUserProfileData!.containsKey('name') &&
            _rawUserProfileData!['name'] is String &&
            (_rawUserProfileData!['name'] as String).isNotEmpty &&
            !(_rawUserProfileData!['name'] as String).contains('-')) {
          displayName = _rawUserProfileData!['name'] as String;
        } else if (_rawUserProfileData!.containsKey('full_name') &&
            _rawUserProfileData!['full_name'] is String &&
            (_rawUserProfileData!['full_name'] as String).isNotEmpty &&
            !(_rawUserProfileData!['full_name'] as String).contains('-')) {
          displayName = _rawUserProfileData!['full_name'] as String;
        } else if (_rawUserProfileData!.containsKey('username') &&
            _rawUserProfileData!['username'] is String &&
            (_rawUserProfileData!['username'] as String).isNotEmpty &&
            !(_rawUserProfileData!['username'] as String).contains('-')) {
          displayName = _rawUserProfileData!['username'] as String;
        } else if (_rawUserProfileData!.containsKey('email') &&
            _rawUserProfileData!['email'] is String &&
            (_rawUserProfileData!['email'] as String).isNotEmpty) {
          // Use email but remove domain part
          final emailParts =
              (_rawUserProfileData!['email'] as String).split('@');
          if (emailParts.isNotEmpty) {
            displayName = emailParts[0];
          }
        }
      }
    }

    // Capitalize first letter of display name
    if (displayName.isNotEmpty) {
      displayName = displayName[0].toUpperCase() + displayName.substring(1);
    }

    // Score is displayed in the chart

    // Get weight and other metrics with better fallback handling
    int age = _userProfile?.age ?? 25; // More reasonable default
    double weight = _userProfile?.weight ?? 65.0; // More reasonable default
    int dailyCalories = 2200;

    // Check if we have actual profile data or just defaults
    bool hasRealAge = _userProfile?.age != null;
    bool hasRealWeight = _userProfile?.weight != null;

    // Try to get calories from calorie data
    if (_calorieData != null) {
      if (_calorieData!.containsKey('target') &&
          _calorieData!['target'] is num) {
        dailyCalories = (_calorieData!['target'] as num).toInt();
      } else if (_calorieData!.containsKey('data') &&
          _calorieData!['data'] is Map<String, dynamic>) {
        final data = _calorieData!['data'] as Map<String, dynamic>;
        if (data.containsKey('target') && data['target'] is num) {
          dailyCalories = (data['target'] as num).toInt();
        }
      }
    }

    return Scaffold(
      body: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(40)),
          color: Colors.white,
        ),
        child: Stack(
          children: [
            // White background container for the main content
            Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(40)),
                color: Colors.white,
              ),
            ),

            // Top black section with background image
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 201,
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(40)),
                  color: Color(0xFF101114),
                ),
                child: Stack(
                  children: [
                    // Background image with overlay
                    Positioned.fill(
                      child: Container(
                        decoration: const BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage('assets/images/homebg.png'),
                            fit: BoxFit.cover,
                            colorFilter: ColorFilter.mode(
                              Color.fromRGBO(16, 17, 20,
                                  0.8), // Semi-transparent dark overlay
                              BlendMode.srcOver,
                            ),
                          ),
                        ),
                        // No additional overlay needed
                      ),
                    ),

                    // Settings button
                    Positioned(
                      top: 50,
                      right: 16,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(18),
                          color: const Color.fromRGBO(35, 37, 43, 1),
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.settings_outlined,
                              color: Colors.white),
                          tooltip: 'Settings',
                          onPressed: () =>
                              Navigator.of(context).pushNamed('/settings'),
                          padding: const EdgeInsets.all(8.0),
                          constraints: const BoxConstraints(),
                          iconSize: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Profile picture - placed at the end of the stack to ensure it's on top of everything
            Positioned(
              top: 145, // Adjusted position for larger profile picture
              left: 0,
              right: 0,
              child: Center(
                child: Material(
                  // Use Material widget to ensure proper elevation
                  elevation: 4,
                  shape: const CircleBorder(),
                  clipBehavior: Clip.antiAlias,
                  color: Colors.transparent,
                  child: GestureDetector(
                    onTap: _editProfilePicture,
                    child: Container(
                      width: 110,
                      height: 110,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                        // Add background color to prevent transparency issues
                        color: Colors.white,
                      ),
                      child: ClipOval(
                        child: profileImageUrl != null &&
                                profileImageUrl.isNotEmpty &&
                                !profileImageUrl.contains('null')
                            ? CachedNetworkImage(
                                // Force a unique key based on the profile picture URL to ensure the image is refreshed
                                key: ValueKey(
                                    'profile_${profileImageUrl}_${DateTime.now().millisecondsSinceEpoch}'),
                                imageUrl: profileImageUrl,
                                fit: BoxFit.cover,
                                // Disable caching to ensure fresh image is loaded
                                cacheManager: null,
                                // Add cache busting parameter to the URL
                                cacheKey:
                                    'profile_${DateTime.now().millisecondsSinceEpoch}',
                                placeholder: (context, url) => const Center(
                                  child: CircularProgressIndicator(
                                      color: Color(0xFFFF7F36)),
                                ),
                                errorWidget: (context, url, error) =>
                                    const Icon(
                                  Icons.person,
                                  color: Color(0xFF101114),
                                  size: 40,
                                ),
                              )
                            : const Icon(Icons.person,
                                color: Color(0xFF101114), size: 40),
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // User name and premium status
            Positioned(
              top: 270, // Adjusted position for larger profile picture
              left: 16,
              right: 16,
              child: Column(
                children: [
                  Text(
                    displayName,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Color(0xFF101114),
                      fontFamily: 'Work Sans',
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.star, color: Color(0xFFFF7F36), size: 20),
                      SizedBox(width: 8),
                      Text(
                        'Premium Member',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Color(0xFF393B42),
                          fontFamily: 'Work Sans',
                          fontSize: 14,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // ACFIT Score section
            Positioned(
              top: 350, // Adjusted position for larger profile picture
              left: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.fromLTRB(16, 20, 16, 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: Colors.white,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.favorite,
                                color: Color(0xFFFF7F36), size: 24),
                            SizedBox(width: 10),
                            Text(
                              'ACFIT Score',
                              style: TextStyle(
                                color: Color(0xFF101114),
                                fontFamily: 'Work Sans',
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: const Color(0xFFFF7F36).withAlpha(25),
                          ),
                          child: const Row(
                            children: [
                              Icon(
                                Icons.trending_up,
                                color: Color(0xFFFF7F36),
                                size: 14,
                              ),
                              SizedBox(width: 4),
                              Text(
                                '+12%',
                                style: TextStyle(
                                  color: Color(0xFFFF7F36),
                                  fontFamily: 'Work Sans',
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    const Text(
                      'Track your fitness progress over time',
                      style: TextStyle(
                        color: Color(0xFF676B74),
                        fontFamily: 'Work Sans',
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      height: 200, // Reduced height to fit better
                      child: BarChart(_buildScoreChart()),
                    ),
                  ],
                ),
              ),
            ),

            // Metrics section
            Positioned(
              bottom: 80, // Position from bottom for better layout
              left: 16,
              right: 16,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: const Color(0xFFF8F8F8),
                ),
                child: Column(
                  children: [
                    const Text(
                      'Your Stats',
                      style: TextStyle(
                        color: Color(0xFF393B42),
                        fontFamily: 'Work Sans',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Show completion prompt if data is missing
                    if (!hasRealAge || !hasRealWeight)
                      Container(
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFF7F36).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: const Color(0xFFFF7F36).withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.info_outline,
                              color: Color(0xFFFF7F36),
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            const Expanded(
                              child: Text(
                                'Complete your profile for better recommendations',
                                style: TextStyle(
                                  color: Color(0xFF393B42),
                                  fontFamily: 'Work Sans',
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // Age metric
                        _buildSimpleMetricCard(
                          icon: Icons.cake_outlined,
                          value: hasRealAge ? '$age' : '--',
                          unit: hasRealAge ? 'yr' : '',
                          label: 'Current Age',
                          color: const Color(0xFF4A6FFF),
                        ),

                        // Weight metric
                        _buildSimpleMetricCard(
                          icon: Icons.fitness_center,
                          value:
                              hasRealWeight ? weight.toStringAsFixed(0) : '--',
                          unit: hasRealWeight ? 'kg' : '',
                          label: 'Weight',
                          color: const Color(0xFFFF7F36),
                        ),

                        // Calories metric
                        _buildSimpleMetricCard(
                          icon: Icons.local_fire_department,
                          value: '$dailyCalories',
                          unit: 'kcal',
                          label: 'Daily Intake',
                          color: const Color(0xFFFF3366),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Simple metric card with clean design
  Widget _buildSimpleMetricCard({
    required IconData icon,
    required String value,
    required String unit,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color.withAlpha(20),
          ),
          child: Icon(icon, color: color, size: 22),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Text(
              value,
              style: const TextStyle(
                color: Color(0xFF101114),
                fontFamily: 'Work Sans',
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              unit,
              style: TextStyle(
                color: color,
                fontFamily: 'Work Sans',
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Color(0xFF676B74),
            fontFamily: 'Work Sans',
            fontSize: 11,
            fontWeight: FontWeight.normal,
          ),
        ),
      ],
    );
  }

  // Chart builder
  BarChartData _buildScoreChart() {
    return BarChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: false,
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: Colors.grey.shade200,
            strokeWidth: 1,
          );
        },
        horizontalInterval: 20,
      ),
      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30, // Increased to prevent clipping
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < _chartDayLabels.length) {
                  return SideTitleWidget(
                    axisSide: meta.axisSide,
                    space: 8, // Add space to prevent clipping
                    child: Text(
                      _chartDayLabels[index],
                      style: const TextStyle(
                        color: Color(0xFF676B74),
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
              showTitles: true,
              interval: 20,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  child: Text(
                    value.toInt().toString(),
                    style: const TextStyle(
                      color: Color(0xFF676B74),
                      fontSize: 10,
                    ),
                  ),
                );
              }),
        ),
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
      ),
      borderData: FlBorderData(show: false),
      barGroups: _chartData.asMap().entries.map((entry) {
        final index = entry.key;
        final spot = entry.value;
        return BarChartGroupData(
          x: index,
          barRods: [
            BarChartRodData(
              toY: spot.y,
              color: const Color(0xFFFF7F36), // Orange color
              width: 20, // Narrower width to prevent overlap with labels
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(2),
                topRight: Radius.circular(2),
              ),
              backDrawRodData: BackgroundBarChartRodData(
                show: true,
                toY: 100,
                color: Colors.grey.shade100,
              ),
            ),
          ],
        );
      }).toList(),
      minY: 0,
      maxY: 100,
      alignment: BarChartAlignment.spaceAround,
      groupsSpace: 12, // Reduced space between bars for better fit
      barTouchData: BarTouchData(
        enabled: true,
        touchTooltipData: BarTouchTooltipData(
          tooltipBgColor: const Color(0xFF101114),
          tooltipRoundedRadius: 4,
          getTooltipItem: (group, groupIndex, rod, rodIndex) {
            return BarTooltipItem(
              '${rod.toY.toInt()}',
              const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontFamily: 'Work Sans',
                fontSize: 12,
              ),
            );
          },
        ),
      ),
    );
  }

  // Removed unused methods
}
