"""
Celery tasks for automated notification sending
"""
from celery import shared_task
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import datetime, timedelta
import logging

from .services import ReminderService
from .models import UserNotificationSettings
from workouts.models import UserWorkoutPlan, WorkoutSchedule
from meals.models import UserMealPlan, MealSchedule

User = get_user_model()
logger = logging.getLogger(__name__)


@shared_task
def send_workout_reminders():
    """Send workout reminders to users based on their schedules"""
    logger.info("Starting workout reminder task")
    
    # Get current time
    now = timezone.now()
    
    # Get all active users with workout plans
    users_with_plans = User.objects.filter(
        userworkoutplan__is_active=True,
        is_active=True
    ).distinct()
    
    sent_count = 0
    
    for user in users_with_plans:
        try:
            # Get user's notification settings
            try:
                settings = UserNotificationSettings.objects.get(user=user)
                if not settings.workout_reminders_enabled:
                    continue
                reminder_minutes = settings.workout_reminder_minutes
            except UserNotificationSettings.DoesNotExist:
                reminder_minutes = 30  # Default
            
            # Get user's active workout plan
            user_workout_plan = UserWorkoutPlan.objects.filter(
                user=user, is_active=True
            ).first()
            
            if not user_workout_plan:
                continue
            
            # Check if user has a workout scheduled soon
            # This is a simplified check - you might want to implement more sophisticated scheduling
            workout_name = user_workout_plan.workout_plan.name
            
            # Send reminder
            if ReminderService.send_workout_reminder(user, workout_name):
                sent_count += 1
                
        except Exception as e:
            logger.error(f"Error sending workout reminder to {user.email}: {e}")
    
    logger.info(f"Workout reminder task completed. Sent {sent_count} reminders")
    return f"Sent {sent_count} workout reminders"


@shared_task
def send_meal_reminders():
    """Send meal reminders to users based on their meal schedules"""
    logger.info("Starting meal reminder task")
    
    now = timezone.now()
    users_with_plans = User.objects.filter(
        usermealplan__is_active=True,
        is_active=True
    ).distinct()
    
    sent_count = 0
    
    for user in users_with_plans:
        try:
            # Get user's notification settings
            try:
                settings = UserNotificationSettings.objects.get(user=user)
                if not settings.meal_reminders_enabled:
                    continue
                reminder_minutes = settings.meal_reminder_minutes
            except UserNotificationSettings.DoesNotExist:
                reminder_minutes = 15  # Default
            
            # Get user's active meal plan
            user_meal_plan = UserMealPlan.objects.filter(
                user=user, is_active=True
            ).first()
            
            if not user_meal_plan:
                continue
            
            # Check if user has a meal scheduled soon
            meal_name = "your planned meal"  # You can make this more specific
            
            # Send reminder
            if ReminderService.send_meal_reminder(user, meal_name):
                sent_count += 1
                
        except Exception as e:
            logger.error(f"Error sending meal reminder to {user.email}: {e}")
    
    logger.info(f"Meal reminder task completed. Sent {sent_count} reminders")
    return f"Sent {sent_count} meal reminders"


@shared_task
def send_hydration_reminders():
    """Send hydration reminders to all users who have them enabled"""
    logger.info("Starting hydration reminder task")
    
    # Get all active users
    users = User.objects.filter(is_active=True)
    sent_count = 0
    
    for user in users:
        try:
            # Get user's notification settings
            try:
                settings = UserNotificationSettings.objects.get(user=user)
                if not settings.hydration_reminders_enabled:
                    continue
            except UserNotificationSettings.DoesNotExist:
                # Create default settings and continue
                UserNotificationSettings.objects.create(user=user)
            
            # Send hydration reminder
            if ReminderService.send_hydration_reminder(user):
                sent_count += 1
                
        except Exception as e:
            logger.error(f"Error sending hydration reminder to {user.email}: {e}")
    
    logger.info(f"Hydration reminder task completed. Sent {sent_count} reminders")
    return f"Sent {sent_count} hydration reminders"


@shared_task
def send_weekly_motivation():
    """Send weekly motivation messages to users"""
    logger.info("Starting weekly motivation task")
    
    # Get all active users
    users = User.objects.filter(is_active=True)
    sent_count = 0
    
    for user in users:
        try:
            # Get user's notification settings
            try:
                settings = UserNotificationSettings.objects.get(user=user)
                if not settings.weekly_motivation_enabled:
                    continue
            except UserNotificationSettings.DoesNotExist:
                # Create default settings and continue
                UserNotificationSettings.objects.create(user=user)
            
            # Send weekly motivation
            if ReminderService.send_weekly_motivation(user):
                sent_count += 1
                
        except Exception as e:
            logger.error(f"Error sending weekly motivation to {user.email}: {e}")
    
    logger.info(f"Weekly motivation task completed. Sent {sent_count} messages")
    return f"Sent {sent_count} weekly motivation messages"


@shared_task
def cleanup_old_notifications():
    """Clean up old notification logs to prevent database bloat"""
    logger.info("Starting notification cleanup task")
    
    from .models import NotificationLog
    
    # Delete notifications older than 90 days
    cutoff_date = timezone.now() - timedelta(days=90)
    deleted_count, _ = NotificationLog.objects.filter(
        created_at__lt=cutoff_date
    ).delete()
    
    logger.info(f"Notification cleanup completed. Deleted {deleted_count} old notifications")
    return f"Deleted {deleted_count} old notifications"
