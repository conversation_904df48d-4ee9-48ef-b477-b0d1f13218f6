"""
Notification services for handling different types of notifications
"""
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db.models import Q
from .models import (
    NotificationLog, UserNotificationSettings, 
    AdminNotificationCampaign, NotificationTemplate
)
from .ai_content import get_random_content
from accounts.models import UserProfile
from workouts.models import UserWorkoutPlan, WorkoutSchedule
from meals.models import UserMealPlan, MealSchedule

User = get_user_model()
logger = logging.getLogger(__name__)


class NotificationService:
    """Main service for handling all notification operations"""
    
    @staticmethod
    def create_notification_log(user, notification_type, title, message, 
                              metadata=None, sent_by_admin=None, admin_message_id=None):
        """Create a notification log entry"""
        return NotificationLog.objects.create(
            user=user,
            notification_type=notification_type,
            title=title,
            message=message,
            metadata=metadata or {},
            sent_by_admin=sent_by_admin,
            admin_message_id=admin_message_id,
            status='SENT',
            sent_at=timezone.now()
        )
    
    @staticmethod
    def get_user_notifications(user, limit=50):
        """Get recent notifications for a user"""
        return NotificationLog.objects.filter(user=user).order_by('-created_at')[:limit]
    
    @staticmethod
    def mark_notification_as_read(notification_id, user):
        """Mark a notification as read"""
        try:
            notification = NotificationLog.objects.get(id=notification_id, user=user)
            notification.mark_as_read()
            return True
        except NotificationLog.DoesNotExist:
            return False


class ReminderService:
    """Service for handling reminder notifications"""
    
    @staticmethod
    def send_workout_reminder(user, workout_name=None, metadata=None):
        """Send workout reminder notification"""
        try:
            settings = UserNotificationSettings.objects.get(user=user)
            if not settings.workout_reminders_enabled:
                return False
        except UserNotificationSettings.DoesNotExist:
            # Create default settings if they don't exist
            settings = UserNotificationSettings.objects.create(user=user)
        
        # Get user's first name or email
        user_name = user.first_name or user.email.split('@')[0]
        
        # Get AI-generated content
        content = get_random_content('WORKOUT_REMINDER', user_name)
        
        # Create notification log
        NotificationService.create_notification_log(
            user=user,
            notification_type='WORKOUT_REMINDER',
            title=content['title'],
            message=content['message'],
            metadata=metadata or {'workout_name': workout_name}
        )
        
        logger.info(f"Workout reminder sent to {user.email}")
        return True
    
    @staticmethod
    def send_meal_reminder(user, meal_name=None, metadata=None):
        """Send meal reminder notification"""
        try:
            settings = UserNotificationSettings.objects.get(user=user)
            if not settings.meal_reminders_enabled:
                return False
        except UserNotificationSettings.DoesNotExist:
            settings = UserNotificationSettings.objects.create(user=user)
        
        user_name = user.first_name or user.email.split('@')[0]
        content = get_random_content('MEAL_REMINDER', user_name)
        
        NotificationService.create_notification_log(
            user=user,
            notification_type='MEAL_REMINDER',
            title=content['title'],
            message=content['message'],
            metadata=metadata or {'meal_name': meal_name}
        )
        
        logger.info(f"Meal reminder sent to {user.email}")
        return True
    
    @staticmethod
    def send_hydration_reminder(user, metadata=None):
        """Send hydration reminder notification"""
        try:
            settings = UserNotificationSettings.objects.get(user=user)
            if not settings.hydration_reminders_enabled:
                return False
        except UserNotificationSettings.DoesNotExist:
            settings = UserNotificationSettings.objects.create(user=user)
        
        user_name = user.first_name or user.email.split('@')[0]
        content = get_random_content('HYDRATION_REMINDER', user_name)
        
        NotificationService.create_notification_log(
            user=user,
            notification_type='HYDRATION_REMINDER',
            title=content['title'],
            message=content['message'],
            metadata=metadata or {}
        )
        
        logger.info(f"Hydration reminder sent to {user.email}")
        return True
    
    @staticmethod
    def send_weekly_motivation(user, metadata=None):
        """Send weekly motivation notification"""
        try:
            settings = UserNotificationSettings.objects.get(user=user)
            if not settings.weekly_motivation_enabled:
                return False
        except UserNotificationSettings.DoesNotExist:
            settings = UserNotificationSettings.objects.create(user=user)
        
        user_name = user.first_name or user.email.split('@')[0]
        content = get_random_content('WEEKLY_MOTIVATION', user_name)
        
        NotificationService.create_notification_log(
            user=user,
            notification_type='WEEKLY_MOTIVATION',
            title=content['title'],
            message=content['message'],
            metadata=metadata or {}
        )
        
        logger.info(f"Weekly motivation sent to {user.email}")
        return True


class AdminNotificationService:
    """Service for handling admin-sent notifications"""
    
    @staticmethod
    def get_target_users(target_type, criteria):
        """Get users based on targeting criteria"""
        if target_type == 'ALL_USERS':
            return User.objects.filter(is_active=True)
        
        elif target_type == 'WORKOUT_PLAN':
            workout_plan_ids = criteria.get('target_workout_plan_ids', [])
            if workout_plan_ids:
                return User.objects.filter(
                    userworkoutplan__workout_plan_id__in=workout_plan_ids,
                    userworkoutplan__is_active=True,
                    is_active=True
                ).distinct()
        
        elif target_type == 'MEAL_PLAN':
            meal_plan_ids = criteria.get('target_meal_plan_ids', [])
            if meal_plan_ids:
                return User.objects.filter(
                    usermealplan__meal_plan_id__in=meal_plan_ids,
                    usermealplan__is_active=True,
                    is_active=True
                ).distinct()
        
        elif target_type == 'QUESTIONNAIRE':
            questionnaire_criteria = criteria.get('target_questionnaire_criteria', {})
            query = Q(is_active=True)
            
            # Build query based on questionnaire criteria
            for field, value in questionnaire_criteria.items():
                if hasattr(UserProfile, field):
                    query &= Q(**{f'userprofile__{field}': value})
            
            return User.objects.filter(query).distinct()
        
        elif target_type == 'CUSTOM':
            user_ids = criteria.get('target_user_ids', [])
            if user_ids:
                return User.objects.filter(id__in=user_ids, is_active=True)
        
        return User.objects.none()
    
    @staticmethod
    def send_admin_notification(campaign_id):
        """Send notifications for an admin campaign"""
        try:
            campaign = AdminNotificationCampaign.objects.get(id=campaign_id)
            
            # Get target users
            target_users = AdminNotificationService.get_target_users(
                campaign.target_type,
                {
                    'target_workout_plan_ids': campaign.target_workout_plan_ids,
                    'target_meal_plan_ids': campaign.target_meal_plan_ids,
                    'target_questionnaire_criteria': campaign.target_questionnaire_criteria,
                    'target_user_ids': campaign.target_user_ids,
                }
            )
            
            campaign.total_recipients = target_users.count()
            sent_count = 0
            failed_count = 0
            
            # Send notifications to each user
            for user in target_users:
                try:
                    # Check if user has admin messages enabled
                    try:
                        settings = UserNotificationSettings.objects.get(user=user)
                        if not settings.admin_messages_enabled:
                            continue
                    except UserNotificationSettings.DoesNotExist:
                        # Create default settings
                        UserNotificationSettings.objects.create(user=user)
                    
                    # Process message with user identifiers
                    processed_message = AdminNotificationService.process_message_identifiers(
                        campaign.message, user
                    )
                    
                    # Create notification log
                    NotificationService.create_notification_log(
                        user=user,
                        notification_type='ADMIN_MESSAGE',
                        title=campaign.title,
                        message=processed_message,
                        sent_by_admin=campaign.created_by,
                        admin_message_id=str(campaign.id),
                        metadata={'campaign_id': str(campaign.id)}
                    )
                    
                    sent_count += 1
                    
                except Exception as e:
                    logger.error(f"Failed to send notification to {user.email}: {e}")
                    failed_count += 1
            
            # Update campaign stats
            campaign.sent_count = sent_count
            campaign.failed_count = failed_count
            campaign.is_sent = True
            campaign.sent_at = timezone.now()
            campaign.save()
            
            logger.info(f"Campaign {campaign.id} sent to {sent_count} users, {failed_count} failed")
            return True
            
        except AdminNotificationCampaign.DoesNotExist:
            logger.error(f"Campaign {campaign_id} not found")
            return False
        except Exception as e:
            logger.error(f"Failed to send campaign {campaign_id}: {e}")
            return False
    
    @staticmethod
    def process_message_identifiers(message, user):
        """Process message identifiers like {user.name}, {user.email}, etc."""
        try:
            # Get user profile
            try:
                profile = user.userprofile
            except:
                profile = None
            
            # Get current plans
            try:
                current_workout_plan = UserWorkoutPlan.objects.filter(
                    user=user, is_active=True
                ).first()
                workout_plan_name = current_workout_plan.workout_plan.name if current_workout_plan else "No active plan"
            except:
                workout_plan_name = "No active plan"
            
            try:
                current_meal_plan = UserMealPlan.objects.filter(
                    user=user, is_active=True
                ).first()
                meal_plan_name = current_meal_plan.meal_plan.name if current_meal_plan else "No active plan"
            except:
                meal_plan_name = "No active plan"
            
            # Replace identifiers
            replacements = {
                '{user.name}': user.first_name or user.email.split('@')[0],
                '{user.first_name}': user.first_name or '',
                '{user.last_name}': user.last_name or '',
                '{user.email}': user.email,
                '{user.workout_plan}': workout_plan_name,
                '{user.meal_plan}': meal_plan_name,
            }
            
            # Add profile-specific replacements if profile exists
            if profile:
                replacements.update({
                    '{user.fitness_goal}': profile.get_primary_fitness_goal_display() if profile.primary_fitness_goal else 'Not set',
                    '{user.fitness_level}': profile.get_fitness_level_display() if profile.fitness_level else 'Not set',
                    '{user.age_group}': profile.get_age_group_display() if profile.age_group else 'Not set',
                })
            
            # Apply replacements
            processed_message = message
            for identifier, value in replacements.items():
                processed_message = processed_message.replace(identifier, str(value))
            
            return processed_message
            
        except Exception as e:
            logger.error(f"Error processing message identifiers: {e}")
            return message  # Return original message if processing fails
