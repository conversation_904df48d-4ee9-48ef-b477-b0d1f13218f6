{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
  {{ block.super }}
  <link rel="stylesheet" href="{% static 'admin/css/dashboard.css' %}">
  <style>
    .documentation-container {
      max-width: 1200px;
      margin: 20px auto;
      padding: 20px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .doc-header {
      text-align: center;
      margin-bottom: 40px;
      padding-bottom: 20px;
      border-bottom: 2px solid #417690;
    }
    
    .doc-header h1 {
      color: #417690;
      font-size: 2.5em;
      margin-bottom: 10px;
    }
    
    .doc-header p {
      color: #666;
      font-size: 1.2em;
    }
    
    .doc-nav {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 30px;
    }
    
    .doc-nav h2 {
      color: #417690;
      margin-bottom: 15px;
    }
    
    .doc-nav ul {
      list-style: none;
      padding: 0;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 10px;
    }
    
    .doc-nav li {
      margin: 0;
    }
    
    .doc-nav a {
      display: block;
      padding: 10px 15px;
      background: #fff;
      color: #417690;
      text-decoration: none;
      border-radius: 4px;
      border: 1px solid #ddd;
      transition: all 0.3s ease;
    }
    
    .doc-nav a:hover {
      background: #417690;
      color: #fff;
      transform: translateY(-2px);
    }
    
    .doc-section {
      margin-bottom: 40px;
      padding: 30px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #417690;
    }
    
    .doc-section h2 {
      color: #417690;
      font-size: 1.8em;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ddd;
    }
    
    .doc-section h3 {
      color: #333;
      font-size: 1.4em;
      margin: 25px 0 15px 0;
    }
    
    .doc-section h4 {
      color: #555;
      font-size: 1.2em;
      margin: 20px 0 10px 0;
    }
    
    .doc-section p {
      line-height: 1.6;
      margin-bottom: 15px;
      color: #333;
    }
    
    .doc-section ul, .doc-section ol {
      margin-bottom: 15px;
      padding-left: 30px;
    }
    
    .doc-section li {
      margin-bottom: 8px;
      line-height: 1.5;
    }
    
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }
    
    .feature-card {
      background: #fff;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #ddd;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .feature-card h4 {
      color: #417690;
      margin-bottom: 10px;
    }

    .url-link {
      display: inline-block;
      background: #417690;
      color: white;
      padding: 8px 15px;
      border-radius: 20px;
      text-decoration: none;
      font-size: 14px;
      margin: 5px 5px 5px 0;
      transition: all 0.3s ease;
    }

    .url-link:hover {
      background: #2c5282;
      transform: translateY(-1px);
      color: white;
      text-decoration: none;
    }

    .url-link i {
      margin-right: 5px;
    }

    .quick-links {
      background: #e8f4f8;
      padding: 15px;
      border-radius: 8px;
      margin: 15px 0;
      border-left: 4px solid #417690;
    }

    .quick-links h4 {
      color: #417690;
      margin-bottom: 10px;
    }

    .admin-section {
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin: 15px 0;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .admin-section h4 {
      color: #417690;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid #eee;
    }

    .function-list {
      list-style: none;
      padding: 0;
    }

    .function-list li {
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .function-list li:last-child {
      border-bottom: none;
    }

    .function-name {
      font-weight: bold;
      color: #333;
    }

    .function-desc {
      color: #666;
      font-size: 14px;
      margin-top: 3px;
    }
    
    .code-block {
      background: #2d3748;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      margin: 15px 0;
      overflow-x: auto;
    }
    
    .warning-box {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-left: 4px solid #f39c12;
      padding: 15px;
      margin: 15px 0;
      border-radius: 4px;
    }
    
    .warning-box h4 {
      color: #e67e22;
      margin-bottom: 10px;
    }
    
    .info-box {
      background: #d1ecf1;
      border: 1px solid #bee5eb;
      border-left: 4px solid #17a2b8;
      padding: 15px;
      margin: 15px 0;
      border-radius: 4px;
    }
    
    .info-box h4 {
      color: #138496;
      margin-bottom: 10px;
    }
    
    .success-box {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      border-left: 4px solid #28a745;
      padding: 15px;
      margin: 15px 0;
      border-radius: 4px;
    }
    
    .success-box h4 {
      color: #155724;
      margin-bottom: 10px;
    }

    .admin-link {
      display: inline-block;
      background: #417690;
      color: white !important;
      padding: 8px 15px;
      border-radius: 20px;
      text-decoration: none;
      margin: 5px 5px 5px 0;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .admin-link:hover {
      background: #2c5282;
      transform: translateY(-1px);
      color: white !important;
    }

    .admin-link i {
      margin-right: 5px;
    }

    .link-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 10px;
      margin: 15px 0;
    }

    .step-list {
      counter-reset: step-counter;
    }
    
    .step-list li {
      counter-increment: step-counter;
      position: relative;
      padding-left: 40px;
      margin-bottom: 20px;
    }
    
    .step-list li::before {
      content: counter(step-counter);
      position: absolute;
      left: 0;
      top: 0;
      background: #417690;
      color: white;
      width: 25px;
      height: 25px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 14px;
    }
    
    .back-to-top {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #417690;
      color: white;
      padding: 10px 15px;
      border-radius: 50px;
      text-decoration: none;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      transition: all 0.3s ease;
    }
    
    .back-to-top:hover {
      background: #2c5282;
      transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
      .documentation-container {
        margin: 10px;
        padding: 15px;
      }
      
      .doc-nav ul {
        grid-template-columns: 1fr;
      }
      
      .feature-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
{% endblock %}

{% block content %}
<div class="documentation-container">
  <div class="doc-header">
    <h1>🏋️ AC-FIT Admin Documentation</h1>
    <p>Complete guide to managing your fitness platform</p>
  </div>

  <div class="doc-nav">
    <h2>📋 Quick Navigation</h2>
    <ul>
      <li><a href="#overview">🏠 Dashboard Overview</a></li>
      <li><a href="#users">👥 User Management</a></li>
      <li><a href="#workouts">💪 Workout Management</a></li>
      <li><a href="#meals">🍽️ Meal Management</a></li>
      <li><a href="#videos">🎥 Video Management</a></li>
      <li><a href="#notifications">🔔 Notification System</a></li>
      <li><a href="#questionnaire">❓ Questionnaire Management</a></li>
      <li><a href="#products">🛒 Product Management</a></li>
      <li><a href="#support">🎧 Support Management</a></li>
      <li><a href="#activity">📊 Activity Logs</a></li>
      <li><a href="#assignment">📝 Plan Assignment</a></li>
      <li><a href="#calendar">📅 Calendar Views</a></li>
      <li><a href="#security">🔒 Security & Permissions</a></li>
    </ul>
  </div>

  <div id="overview" class="doc-section">
    <h2>🏠 Dashboard Overview</h2>
    <p>The AC-FIT admin dashboard is your central hub for managing the entire fitness platform. Here's what you can access:</p>

    <div class="quick-links">
      <h4>🔗 Quick Access Links</h4>
      <a href="{% url 'acfit_admin:dashboard' %}" class="url-link" target="_blank">
        <i class="fas fa-home"></i> Main Dashboard
      </a>
      <a href="{% url 'acfit_admin:users' %}" class="url-link" target="_blank">
        <i class="fas fa-users"></i> All Users
      </a>
      <a href="{% url 'acfit_admin:workout-plans' %}" class="url-link" target="_blank">
        <i class="fas fa-dumbbell"></i> Workout Plans
      </a>
      <a href="{% url 'acfit_admin:meal-plans' %}" class="url-link" target="_blank">
        <i class="fas fa-utensils"></i> Meal Plans
      </a>
      <a href="{% url 'acfit_admin:assign-plans' %}" class="url-link" target="_blank">
        <i class="fas fa-user-plus"></i> Assign Plans
      </a>
    </div>

    <div class="feature-grid">
      <div class="feature-card">
        <h4>📊 Key Metrics</h4>
        <p>View total users, active workout plans, meal plans, and recent activity at a glance.</p>
        <ul>
          <li><strong>User Count:</strong> Total registered users</li>
          <li><strong>Active Plans:</strong> Currently assigned workout/meal plans</li>
          <li><strong>Activity Logs:</strong> Recent user interactions</li>
          <li><strong>Feedback Count:</strong> Unresolved user feedback</li>
        </ul>
      </div>
      <div class="feature-card">
        <h4>🚀 Quick Actions</h4>
        <p>Access frequently used functions like assigning plans, managing questionnaires, and viewing feedback.</p>
        <ul>
          <li><strong>Assign Plans:</strong> Bulk assign workout/meal plans</li>
          <li><strong>Manage Questionnaire:</strong> Add/edit user questions</li>
          <li><strong>View Feedback:</strong> Handle user support requests</li>
          <li><strong>Activity Logs:</strong> Monitor system usage</li>
        </ul>
      </div>
      <div class="feature-card">
        <h4>📈 Recent Activity</h4>
        <p>Monitor recent user registrations, feedback submissions, and system activity.</p>
        <ul>
          <li><strong>Recent Users:</strong> Last 10 user registrations</li>
          <li><strong>Recent Feedback:</strong> Latest user support requests</li>
          <li><strong>Recent Plans:</strong> Newly created workout/meal plans</li>
          <li><strong>Activity Logs:</strong> Latest user interactions</li>
        </ul>
      </div>
      <div class="feature-card">
        <h4>🔗 Direct Links</h4>
        <p>Quick access to all major admin functions through the action buttons.</p>
        <ul>
          <li><strong>Calendar Views:</strong> Visual schedule management</li>
          <li><strong>Video Management:</strong> Upload workout videos</li>
          <li><strong>Product Dashboard:</strong> Manage fitness products</li>
          <li><strong>Program Days:</strong> View detailed schedules</li>
        </ul>
      </div>
    </div>

    <div class="admin-section">
      <h4>🎯 Dashboard Functions</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">User Management</div>
          <div class="function-desc">View, edit, and delete user accounts. Monitor user progress and scores.</div>
        </li>
        <li>
          <div class="function-name">Plan Assignment</div>
          <div class="function-desc">Assign workout and meal plans to users based on their profiles and goals.</div>
        </li>
        <li>
          <div class="function-name">Content Management</div>
          <div class="function-desc">Create and manage workout plans, meal plans, exercises, and recipes.</div>
        </li>
        <li>
          <div class="function-name">Support System</div>
          <div class="function-desc">Handle user feedback, manage FAQs, and provide customer support.</div>
        </li>
        <li>
          <div class="function-name">Analytics & Monitoring</div>
          <div class="function-desc">Track user activity, monitor system performance, and analyze usage patterns.</div>
        </li>
      </ul>
    </div>

    <div class="info-box">
      <h4>💡 Pro Tip</h4>
      <p>The dashboard automatically refreshes key metrics. Use the action buttons for quick access to common tasks. Bookmark frequently used admin URLs for faster navigation.</p>
    </div>
  </div>

  <div id="users" class="doc-section">
    <h2>👥 User Management</h2>
    <p>Comprehensive user management system for handling user accounts, profiles, progress tracking, and scoring.</p>

    <div class="quick-links">
      <h4>🔗 User Management Links</h4>
      <a href="{% url 'acfit_admin:users' %}" class="url-link" target="_blank">
        <i class="fas fa-users"></i> All Users
      </a>
      <a href="/acfit-admin/accounts/userprofile/" class="url-link" target="_blank">
        <i class="fas fa-user-circle"></i> User Profiles
      </a>
      <a href="/acfit-admin/accounts/userprogress/" class="url-link" target="_blank">
        <i class="fas fa-chart-line"></i> User Progress
      </a>
      <a href="/acfit-admin/accounts/userscore/" class="url-link" target="_blank">
        <i class="fas fa-trophy"></i> User Scores
      </a>
      <a href="{% url 'acfit_admin:add-user' %}" class="url-link" target="_blank">
        <i class="fas fa-user-plus"></i> Add New User
      </a>
    </div>

    <div class="admin-section">
      <h4>👤 User Accounts</h4>
      <p>Manage user authentication and basic account information:</p>
      <ul class="function-list">
        <li>
          <div class="function-name">View All Users</div>
          <div class="function-desc">Access complete user list with search and filtering capabilities. Sort by registration date, activity level, or status.</div>
        </li>
        <li>
          <div class="function-name">Edit User Details</div>
          <div class="function-desc">Modify user email, username, permissions, and account status. Update personal information and preferences.</div>
        </li>
        <li>
          <div class="function-name">User Status Management</div>
          <div class="function-desc">Activate/deactivate accounts, manage superuser permissions, and control access levels.</div>
        </li>
        <li>
          <div class="function-name">Account Deletion</div>
          <div class="function-desc">Remove user accounts with proper confirmation and data cleanup procedures.</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>📋 User Profiles</h4>
      <p>Detailed user profile management including:</p>
      <ul class="function-list">
        <li>
          <div class="function-name">Personal Information</div>
          <div class="function-desc">Age, gender, height, weight, fitness goals, and experience level. Essential for plan matching.</div>
        </li>
        <li>
          <div class="function-name">Health Conditions</div>
          <div class="function-desc">Medical considerations, injuries, and limitations that affect workout and meal plan assignments.</div>
        </li>
        <li>
          <div class="function-name">Dietary Preferences</div>
          <div class="function-desc">Keto, intermittent fasting, vegetarian, allergies, and other nutritional preferences.</div>
        </li>
        <li>
          <div class="function-name">Active Plan Tracking</div>
          <div class="function-desc">Current workout and meal plan assignments with start dates and progress status.</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>📊 Progress Tracking</h4>
      <p>Monitor user progress across multiple metrics:</p>
      <ul class="function-list">
        <li>
          <div class="function-name">Workout Metrics</div>
          <div class="function-desc">Completed workouts, total exercise minutes, calories burned, and workout consistency.</div>
        </li>
        <li>
          <div class="function-name">Nutrition Metrics</div>
          <div class="function-desc">Meals completed, calories consumed, protein/carbs/fat intake, and hydration tracking.</div>
        </li>
        <li>
          <div class="function-name">Activity Streaks</div>
          <div class="function-desc">Current streak days, longest streak achieved, and streak maintenance patterns.</div>
        </li>
        <li>
          <div class="function-name">Goal Progress</div>
          <div class="function-desc">Progress toward weight loss, muscle gain, endurance, or strength goals.</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>🏆 User Scoring System</h4>
      <p>Comprehensive scoring system that motivates users:</p>
      <ul class="function-list">
        <li>
          <div class="function-name">Total Score Calculation</div>
          <div class="function-desc">Overall user performance score combining workout, nutrition, streak, and goal achievements.</div>
        </li>
        <li>
          <div class="function-name">Component Scores</div>
          <div class="function-desc">Individual scores for workouts (40%), nutrition (30%), streaks (20%), and goals (10%).</div>
        </li>
        <li>
          <div class="function-name">Achievement System</div>
          <div class="function-desc">Milestones, perfect weeks, weight goals, and special accomplishments tracking.</div>
        </li>
        <li>
          <div class="function-name">Points & Rewards</div>
          <div class="function-desc">Points earned for activities, bonus multipliers, and reward system integration.</div>
        </li>
      </ul>
    </div>

    <div class="warning-box">
      <h4>⚠️ Important Privacy Considerations</h4>
      <p>Always verify user consent before modifying personal health information or deleting accounts. Follow GDPR and privacy regulations when handling user data.</p>
    </div>
  </div>

  <div id="workouts" class="doc-section">
    <h2>💪 Workout Management</h2>
    <p>Complete workout system management including plans, exercises, schedules, and user assignments.</p>

    <div class="quick-links">
      <h4>🔗 Workout Management Links</h4>
      <a href="{% url 'acfit_admin:workout-plans' %}" class="url-link" target="_blank">
        <i class="fas fa-dumbbell"></i> Workout Plans
      </a>
      <a href="/acfit-admin/workouts/workoutday/" class="url-link" target="_blank">
        <i class="fas fa-calendar-day"></i> Workout Days
      </a>
      <a href="/acfit-admin/workouts/exercise/" class="url-link" target="_blank">
        <i class="fas fa-running"></i> Exercise Database
      </a>
      <a href="{% url 'acfit_admin:user-workout-plans' %}" class="url-link" target="_blank">
        <i class="fas fa-user-clock"></i> User Assignments
      </a>
      <a href="/acfit-admin/workouts/workoutvideo/" class="url-link" target="_blank">
        <i class="fas fa-video"></i> Workout Videos
      </a>
      <a href="{% url 'acfit_admin:workout-calendar' %}" class="url-link" target="_blank">
        <i class="fas fa-calendar-alt"></i> Workout Calendar
      </a>
    </div>

    <div class="admin-section">
      <h4>🏋️ Workout Plans</h4>
      <p>Create and manage comprehensive workout programs:</p>
      <ul class="function-list">
        <li>
          <div class="function-name">Plan Structure</div>
          <div class="function-desc">Define workout duration (weeks), frequency (workouts per week), and overall program structure.</div>
        </li>
        <li>
          <div class="function-name">Target Audience</div>
          <div class="function-desc">Set fitness level (beginner/intermediate/advanced), goals (weight loss/muscle gain), location (home/gym), and equipment requirements.</div>
        </li>
        <li>
          <div class="function-name">Health Accommodations</div>
          <div class="function-desc">Specify which health conditions the plan accommodates (back pain, knee issues, pregnancy, etc.).</div>
        </li>
        <li>
          <div class="function-name">Demographics</div>
          <div class="function-desc">Target specific age groups (18-25, 26-35, 36-50, 50+) and gender preferences.</div>
        </li>
        <li>
          <div class="function-name">Media & Branding</div>
          <div class="function-desc">Add cover images, promotional content, and visual branding for the workout plan.</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>📅 Workout Days</h4>
      <p>Design individual workout sessions:</p>
      <ul class="function-list">
        <li>
          <div class="function-name">Session Structure</div>
          <div class="function-desc">Organize exercises into sections: warm-up (5-10 min), main workout (20-45 min), cool-down (5-10 min).</div>
        </li>
        <li>
          <div class="function-name">Exercise Programming</div>
          <div class="function-desc">Add exercises with specific sets, reps, weight, duration, and rest periods between exercises.</div>
        </li>
        <li>
          <div class="function-name">Progression Planning</div>
          <div class="function-desc">Set difficulty progression throughout the plan - increase intensity, volume, or complexity over time.</div>
        </li>
        <li>
          <div class="function-name">Alternative Options</div>
          <div class="function-desc">Provide exercise modifications for different fitness levels and equipment availability.</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>🏃 Exercise Database</h4>
      <p>Comprehensive exercise library management:</p>
      <ul class="function-list">
        <li>
          <div class="function-name">Exercise Information</div>
          <div class="function-desc">Name, detailed description, primary and secondary muscle groups, difficulty level, and exercise category.</div>
        </li>
        <li>
          <div class="function-name">Visual Demonstrations</div>
          <div class="function-desc">Upload images, videos, and GIFs showing proper form and technique for each exercise.</div>
        </li>
        <li>
          <div class="function-name">Equipment Requirements</div>
          <div class="function-desc">Specify required equipment, alternatives, and bodyweight modifications for home workouts.</div>
        </li>
        <li>
          <div class="function-name">Safety & Form</div>
          <div class="function-desc">Important form cues, safety precautions, common mistakes, and injury prevention tips.</div>
        </li>
        <li>
          <div class="function-name">Exercise Variations</div>
          <div class="function-desc">Easier progressions for beginners and advanced variations for experienced users.</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>👤 User Workout Plans</h4>
      <p>Manage individual user workout assignments:</p>
      <ul class="function-list">
        <li>
          <div class="function-name">Plan Assignment</div>
          <div class="function-desc">Assign workout plans to specific users based on their profile, goals, and preferences.</div>
        </li>
        <li>
          <div class="function-name">Schedule Management</div>
          <div class="function-desc">Set start dates, duration, and customize workout schedules for individual users.</div>
        </li>
        <li>
          <div class="function-name">Personal Customization</div>
          <div class="function-desc">Modify plans for individual user needs, injuries, equipment limitations, or preferences.</div>
        </li>
        <li>
          <div class="function-name">Progress Monitoring</div>
          <div class="function-desc">Track completion rates, performance metrics, and user engagement with assigned workouts.</div>
        </li>
        <li>
          <div class="function-name">Sync Control</div>
          <div class="function-desc">Control how global plan updates affect user schedules - sync all changes, new content only, or no sync.</div>
        </li>
      </ul>
    </div>

    <div class="success-box">
      <h4>✅ Workout Plan Best Practices</h4>
      <p>Always test workout plans thoroughly before assigning them to users. Ensure proper progression, safety considerations, and clear instructions. Include modifications for different fitness levels.</p>
    </div>
  </div>

  <div id="meals" class="doc-section">
    <h2>🍽️ Meal Management</h2>
    <p>Comprehensive nutrition management system for meal plans, recipes, and dietary tracking.</p>

    <h3>Meal Plans</h3>
    <p>Create structured nutrition programs:</p>
    <ul>
      <li><strong>Plan Structure:</strong> Define daily calorie targets and meal frequency</li>
      <li><strong>Dietary Preferences:</strong> Support for keto, vegetarian, and other diets</li>
      <li><strong>Health Considerations:</strong> Accommodate allergies and medical conditions</li>
      <li><strong>Macronutrient Balance:</strong> Set protein, carb, and fat ratios</li>
      <li><strong>Hydration Goals:</strong> Include daily water intake targets</li>
    </ul>

    <h3>Individual Meals</h3>
    <p>Manage recipe database and meal details:</p>
    <ul>
      <li><strong>Nutritional Info:</strong> Calories, macronutrients, and micronutrients</li>
      <li><strong>Preparation Details:</strong> Prep time, cooking time, and difficulty</li>
      <li><strong>Ingredients:</strong> Complete ingredient lists with quantities</li>
      <li><strong>Instructions:</strong> Step-by-step cooking instructions</li>
      <li><strong>Media:</strong> Food photos and cooking videos</li>
      <li><strong>Categories:</strong> Meal type, cuisine, and dietary tags</li>
    </ul>

    <h3>Daily Meal Plans</h3>
    <p>Organize meals into daily schedules:</p>
    <ul>
      <li><strong>Meal Timing:</strong> Breakfast, lunch, dinner, and snacks</li>
      <li><strong>Portion Control:</strong> Serving sizes and calorie distribution</li>
      <li><strong>Variety:</strong> Ensure diverse nutrition and flavors</li>
      <li><strong>Preparation Flow:</strong> Optimize cooking and prep sequences</li>
    </ul>

    <h3>User Meal Plans</h3>
    <p>Personalized nutrition assignments:</p>
    <ul>
      <li><strong>Individual Customization:</strong> Adjust for personal preferences and restrictions</li>
      <li><strong>Progress Tracking:</strong> Monitor meal completion and nutrition intake</li>
      <li><strong>Substitutions:</strong> Allow ingredient and meal swaps</li>
      <li><strong>Shopping Lists:</strong> Generate grocery lists from meal plans</li>
    </ul>

    <div class="info-box">
      <h4>💡 Nutrition Tip</h4>
      <p>Always verify nutritional calculations and consider consulting with registered dietitians for specialized meal plans.</p>
    </div>
  </div>

  <div id="videos" class="doc-section">
    <h2>🎥 Video Management</h2>
    <p>Comprehensive video system for workout demonstrations, tutorials, and educational content.</p>

    <div class="quick-links">
      <h4>🔗 Video Management Links</h4>
      <a href="/acfit-admin/workouts/workoutvideo/" class="url-link" target="_blank">
        <i class="fas fa-video"></i> All Workout Videos
      </a>
      <a href="/acfit-admin/workouts/workoutvideo/add/" class="url-link" target="_blank">
        <i class="fas fa-plus-circle"></i> Add New Video
      </a>
      <a href="{% url 'acfit_admin:workout-plans' %}" class="url-link" target="_blank">
        <i class="fas fa-dumbbell"></i> Workout Plans (for linking)
      </a>
    </div>

    <div class="admin-section">
      <h4>📤 Video Upload Process</h4>
      <ol class="step-list">
        <li><strong>Access Video Admin:</strong> Navigate to Workout Videos section in admin or use the direct link above</li>
        <li><strong>Click "Add Video":</strong> Start the video upload process</li>
        <li><strong>Select Workout Plan:</strong> Choose which workout plan the video belongs to (required)</li>
        <li><strong>Upload Video File:</strong> Select MP4 file (recommended format, max 500MB)</li>
        <li><strong>Add Thumbnail:</strong> Upload a preview image (JPG/PNG, 1280x720 recommended)</li>
        <li><strong>Set Video Details:</strong> Add title, description, and display order</li>
        <li><strong>Set Duration:</strong> Enter video duration in seconds (optional, auto-detected)</li>
        <li><strong>Save and Preview:</strong> Test video playback and thumbnail display in admin</li>
      </ol>
    </div>

    <div class="admin-section">
      <h4>📋 Video Organization</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Workout Plan Association</div>
          <div class="function-desc">Videos are linked to specific workout plans. Users see videos when they access their assigned workout plans.</div>
        </li>
        <li>
          <div class="function-name">Display Order</div>
          <div class="function-desc">Control the sequence videos appear to users. Lower numbers appear first (1, 2, 3, etc.).</div>
        </li>
        <li>
          <div class="function-name">Multiple Videos per Plan</div>
          <div class="function-desc">Each workout plan can have multiple instructional videos - overview, specific exercises, form tips, etc.</div>
        </li>
        <li>
          <div class="function-name">Duration Tracking</div>
          <div class="function-desc">System automatically tracks video length for user progress and engagement analytics.</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>⚙️ Video Features</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Admin Preview</div>
          <div class="function-desc">Watch videos directly in the admin interface to verify quality and content before publishing.</div>
        </li>
        <li>
          <div class="function-name">Custom Thumbnails</div>
          <div class="function-desc">Upload custom thumbnail images for better user experience and consistent branding.</div>
        </li>
        <li>
          <div class="function-name">Mobile Optimization</div>
          <div class="function-desc">Videos automatically work across all devices - desktop, tablet, and mobile phones.</div>
        </li>
        <li>
          <div class="function-name">Advanced Playback</div>
          <div class="function-desc">Full video player with speed control (0.5x, 1x, 1.25x, 1.5x, 2x), fullscreen, and progress tracking.</div>
        </li>
        <li>
          <div class="function-name">User Analytics</div>
          <div class="function-desc">Track video views, completion rates, and user engagement for content optimization.</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>🎬 Video Content Guidelines</h4>
      <div class="feature-grid">
        <div class="feature-card">
          <h4>📹 Video Quality Standards</h4>
          <ul>
            <li><strong>Resolution:</strong> 1080p minimum (1920x1080)</li>
            <li><strong>Frame Rate:</strong> 30fps or 60fps</li>
            <li><strong>Lighting:</strong> Well-lit, consistent lighting</li>
            <li><strong>Audio:</strong> Clear, noise-free audio</li>
            <li><strong>Duration:</strong> 2-15 minutes optimal</li>
            <li><strong>Format:</strong> MP4 with H.264 codec</li>
          </ul>
        </div>
        <div class="feature-card">
          <h4>🖼️ Thumbnail Best Practices</h4>
          <ul>
            <li><strong>Resolution:</strong> 1280x720 (16:9 aspect ratio)</li>
            <li><strong>Content:</strong> Show the exercise being performed</li>
            <li><strong>Branding:</strong> Consistent style and colors</li>
            <li><strong>Text:</strong> Minimal, readable text overlay</li>
            <li><strong>Quality:</strong> High-quality, sharp images</li>
            <li><strong>Format:</strong> JPG or PNG</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="admin-section">
      <h4>📝 Video Content Types</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Workout Overview</div>
          <div class="function-desc">Introduction to the workout plan, goals, and what users can expect.</div>
        </li>
        <li>
          <div class="function-name">Exercise Demonstrations</div>
          <div class="function-desc">Proper form and technique for specific exercises in the plan.</div>
        </li>
        <li>
          <div class="function-name">Modification Tutorials</div>
          <div class="function-desc">How to modify exercises for different fitness levels or equipment limitations.</div>
        </li>
        <li>
          <div class="function-name">Motivational Content</div>
          <div class="function-desc">Encouragement, tips, and motivation to help users stay committed.</div>
        </li>
      </ul>
    </div>

    <div class="warning-box">
      <h4>⚠️ Technical Considerations</h4>
      <p><strong>File Size:</strong> Keep videos under 500MB for faster loading. <strong>Compression:</strong> Use H.264 codec with moderate compression. <strong>Testing:</strong> Always test video playback on different devices before publishing.</p>
    </div>
  </div>

  <div id="notifications" class="doc-section">
    <h2>🔔 Notification System</h2>
    <p>Comprehensive notification platform combining AI-generated reminders with admin-controlled campaigns. Uses local notifications only (no Firebase required) for better privacy and reliability.</p>

    <div class="quick-links">
      <h4>🔗 Notification Management Links</h4>
      <a href="/acfit-admin/notifications/adminnotificationcampaign/" class="url-link" target="_blank">
        <i class="fas fa-bell"></i> Notification Campaigns
      </a>
      <a href="/acfit-admin/notifications/usernotificationsettings/" class="url-link" target="_blank">
        <i class="fas fa-cog"></i> User Settings
      </a>
      <a href="/acfit-admin/notifications/notificationlog/" class="url-link" target="_blank">
        <i class="fas fa-history"></i> Notification History
      </a>
      <a href="/acfit-admin/notifications/notificationtemplate/" class="url-link" target="_blank">
        <i class="fas fa-file-text"></i> AI Templates
      </a>
      <a href="/api/notifications/admin/documentation/" class="url-link" target="_blank">
        <i class="fas fa-book"></i> Detailed Documentation
      </a>
    </div>

    <div class="admin-section">
      <h4>🏗️ How The System Works</h4>
      <p>AC-FIT's notification system uses <strong>local notifications only</strong> - no external services required:</p>
      <ol class="step-list">
        <li><strong>Server Generation:</strong> Backend creates notifications based on user schedules and admin campaigns</li>
        <li><strong>Background Sync:</strong> Flutter app syncs with server every hour to fetch new notifications</li>
        <li><strong>Local Display:</strong> Notifications displayed immediately using device's local notification system</li>
        <li><strong>User Control:</strong> Users manage all notification types through in-app settings</li>
      </ol>
    </div>

    <div class="feature-grid">
      <div class="feature-card">
        <h4>🤖 AI-Generated Reminders</h4>
        <p>Automated reminders with 34 unique AI messages:</p>
        <ul>
          <li><strong>Workout Reminders:</strong> 8 motivational messages, 30 min before workouts</li>
          <li><strong>Meal Reminders:</strong> 8 food-focused messages, 15 min before meals</li>
          <li><strong>Hydration Reminders:</strong> 8 water intake messages, every 2 hours</li>
          <li><strong>Weekly Motivation:</strong> 10 inspirational messages, weekly schedule</li>
        </ul>
      </div>
      <div class="feature-card">
        <h4>👨‍💼 Admin Campaigns</h4>
        <p>Targeted messaging with personalization:</p>
        <ul>
          <li><strong>User Targeting:</strong> All users, specific plans, or questionnaire responses</li>
          <li><strong>Message Identifiers:</strong> Use {user.name}, {user.workout_plan}, etc.</li>
          <li><strong>Campaign Tracking:</strong> Monitor delivery and engagement</li>
          <li><strong>Separate Toggle:</strong> Users can disable admin messages independently</li>
        </ul>
      </div>
      <div class="feature-card">
        <h4>📱 Local Notifications</h4>
        <p>Privacy-focused notification delivery:</p>
        <ul>
          <li><strong>No External Services:</strong> No Firebase or push services needed</li>
          <li><strong>Better Privacy:</strong> No data sent to external servers</li>
          <li><strong>Offline Capable:</strong> Works without internet connection</li>
          <li><strong>User Control:</strong> Complete control over notification settings</li>
        </ul>
      </div>
      <div class="feature-card">
        <h4>⚙️ User Experience</h4>
        <p>Comprehensive notification management:</p>
        <ul>
          <li><strong>Notification Icon:</strong> Home screen icon with unread badge</li>
          <li><strong>History Screen:</strong> View all past notifications</li>
          <li><strong>Individual Controls:</strong> Toggle each notification type</li>
          <li><strong>Timing Settings:</strong> Customize reminder timing</li>
        </ul>
      </div>
    </div>

    <div class="admin-section">
      <h4>📊 Creating Admin Campaigns</h4>
      <ol class="step-list">
        <li><strong>Access Campaigns:</strong> Click "Notifications" in admin header or use direct link above</li>
        <li><strong>Create Campaign:</strong> Click "Add Admin notification campaign" button</li>
        <li><strong>Set Title & Message:</strong> Add campaign title and personalized message content</li>
        <li><strong>Choose Targeting:</strong> Select all users, specific plans, or questionnaire criteria</li>
        <li><strong>Use Identifiers:</strong> Include {user.name}, {user.workout_plan}, {user.meal_plan} for personalization</li>
        <li><strong>Send or Save:</strong> Send immediately or save as draft for later</li>
        <li><strong>Monitor Results:</strong> Track delivery status and user engagement</li>
      </ol>
    </div>

    <div class="admin-section">
      <h4>🎯 Message Personalization Examples</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Basic Personalization</div>
          <div class="function-desc">"Hey {user.name}! Ready to crush your workout today?"</div>
        </li>
        <li>
          <div class="function-name">Plan-Specific Messages</div>
          <div class="function-desc">"Your {user.workout_plan} session is starting soon, {user.first_name}!"</div>
        </li>
        <li>
          <div class="function-name">Goal-Oriented Content</div>
          <div class="function-desc">"You're making great progress on your {user.fitness_goal} journey, {user.name}!"</div>
        </li>
        <li>
          <div class="function-name">Meal Plan Integration</div>
          <div class="function-desc">"Time for your {user.meal_plan} meal! Fuel your body right, {user.first_name}."</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>🔔 Notification Channels & Priorities</h4>
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <tr style="background: #f8f9fa;">
          <th style="padding: 12px; border: 1px solid #ddd;">Channel</th>
          <th style="padding: 12px; border: 1px solid #ddd;">Priority</th>
          <th style="padding: 12px; border: 1px solid #ddd;">Default Timing</th>
          <th style="padding: 12px; border: 1px solid #ddd;">User Control</th>
        </tr>
        <tr>
          <td style="padding: 12px; border: 1px solid #ddd;"><strong>Workout Reminders</strong></td>
          <td style="padding: 12px; border: 1px solid #ddd;">High</td>
          <td style="padding: 12px; border: 1px solid #ddd;">30 minutes before</td>
          <td style="padding: 12px; border: 1px solid #ddd;">Can disable, adjust timing (5-120 min)</td>
        </tr>
        <tr>
          <td style="padding: 12px; border: 1px solid #ddd;"><strong>Meal Reminders</strong></td>
          <td style="padding: 12px; border: 1px solid #ddd;">High</td>
          <td style="padding: 12px; border: 1px solid #ddd;">15 minutes before</td>
          <td style="padding: 12px; border: 1px solid #ddd;">Can disable, adjust timing (5-60 min)</td>
        </tr>
        <tr>
          <td style="padding: 12px; border: 1px solid #ddd;"><strong>Hydration Reminders</strong></td>
          <td style="padding: 12px; border: 1px solid #ddd;">Default</td>
          <td style="padding: 12px; border: 1px solid #ddd;">Every 2 hours</td>
          <td style="padding: 12px; border: 1px solid #ddd;">Can disable, adjust interval (1-12 hours)</td>
        </tr>
        <tr>
          <td style="padding: 12px; border: 1px solid #ddd;"><strong>Weekly Motivation</strong></td>
          <td style="padding: 12px; border: 1px solid #ddd;">Default</td>
          <td style="padding: 12px; border: 1px solid #ddd;">Once per week</td>
          <td style="padding: 12px; border: 1px solid #ddd;">Can disable, choose day and time</td>
        </tr>
        <tr>
          <td style="padding: 12px; border: 1px solid #ddd;"><strong>Admin Messages</strong></td>
          <td style="padding: 12px; border: 1px solid #ddd;">High</td>
          <td style="padding: 12px; border: 1px solid #ddd;">Immediate</td>
          <td style="padding: 12px; border: 1px solid #ddd;">Can disable separately from reminders</td>
        </tr>
      </table>
    </div>

    <div class="admin-section">
      <h4>🤖 AI Content Pool Management</h4>
      <p>The system includes 34 pre-written AI messages that provide variety and engagement:</p>
      <ul class="function-list">
        <li>
          <div class="function-name">Workout Messages (8 unique)</div>
          <div class="function-desc">"Time to crush your workout! 💪 Your future self will thank you." | "Ready to get stronger? Your workout is starting soon!"</div>
        </li>
        <li>
          <div class="function-name">Meal Messages (8 unique)</div>
          <div class="function-desc">"Fuel your body right! 🍎 Your meal is ready to be enjoyed." | "Time to nourish yourself! Your planned meal awaits."</div>
        </li>
        <li>
          <div class="function-name">Hydration Messages (8 unique)</div>
          <div class="function-desc">"Stay hydrated, stay healthy! 💧 Time for some water." | "Your body needs water to perform its best! Drink up!"</div>
        </li>
        <li>
          <div class="function-name">Motivation Messages (10 unique)</div>
          <div class="function-desc">"You're making amazing progress! Keep up the fantastic work! 🌟" | "Every step forward is a victory! You're doing great!"</div>
        </li>
      </ul>
    </div>

    <div class="warning-box">
      <h4>⚠️ Best Practices</h4>
      <p><strong>Personalization:</strong> Always use identifiers to make messages personal. <strong>Timing:</strong> Consider user time zones when sending campaigns. <strong>Frequency:</strong> Avoid overwhelming users with too many admin messages. <strong>Value:</strong> Ensure each notification provides value to the user.</p>
    </div>
  </div>

  <div id="questionnaire" class="doc-section">
    <h2>❓ Questionnaire Management</h2>
    <p>Dynamic questionnaire system for collecting user information and personalizing plan assignments.</p>

    <div class="quick-links">
      <h4>🔗 Questionnaire Management Links</h4>
      <a href="{% url 'acfit_admin:questionnaire-manager' %}" class="url-link" target="_blank">
        <i class="fas fa-question-circle"></i> Questionnaire Manager
      </a>
      <a href="{% url 'acfit_admin:add-question' %}" class="url-link" target="_blank">
        <i class="fas fa-plus-circle"></i> Add New Question
      </a>
      <a href="/acfit-admin/admin_site/questionnairequestion/" class="url-link" target="_blank">
        <i class="fas fa-list"></i> All Questions
      </a>
      <a href="/acfit-admin/admin_site/questionnaireoption/" class="url-link" target="_blank">
        <i class="fas fa-check-square"></i> Answer Options
      </a>
    </div>

    <div class="admin-section">
      <h4>❓ Question Types Available</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Text Input</div>
          <div class="function-desc">Free-form text responses for open-ended questions like "Describe your fitness goals"</div>
        </li>
        <li>
          <div class="function-name">Number Input</div>
          <div class="function-desc">Numeric values for age, weight, height, years of experience, etc.</div>
        </li>
        <li>
          <div class="function-name">Single Choice (Radio)</div>
          <div class="function-desc">Select one option from multiple choices - fitness level, gender, primary goal</div>
        </li>
        <li>
          <div class="function-name">Multiple Choice (Checkbox)</div>
          <div class="function-desc">Select multiple options - health conditions, available equipment, dietary preferences</div>
        </li>
        <li>
          <div class="function-name">Dropdown Select</div>
          <div class="function-desc">Choose from a dropdown list - location preference, time availability, experience level</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>⚙️ Question Configuration</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Field Name</div>
          <div class="function-desc">Internal identifier for data storage (e.g., "fitness_level", "health_conditions"). Use lowercase with underscores.</div>
        </li>
        <li>
          <div class="function-name">Display Text</div>
          <div class="function-desc">User-facing question text that appears in the questionnaire form. Keep clear and concise.</div>
        </li>
        <li>
          <div class="function-name">Required Status</div>
          <div class="function-desc">Mark questions as mandatory (must be answered) or optional (can be skipped).</div>
        </li>
        <li>
          <div class="function-name">Display Order</div>
          <div class="function-desc">Question sequence in the form (1, 2, 3, etc.). Lower numbers appear first.</div>
        </li>
        <li>
          <div class="function-name">Plan Assignment Impact</div>
          <div class="function-desc">Mark questions that affect workout/meal plan selection for automatic assignment.</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>📝 Answer Options Management</h4>
      <p>For choice-based questions (radio, checkbox, dropdown), manage answer options:</p>
      <ul class="function-list">
        <li>
          <div class="function-name">Option Text</div>
          <div class="function-desc">Display text that users see (e.g., "Beginner", "Intermediate", "Advanced")</div>
        </li>
        <li>
          <div class="function-name">Option Value</div>
          <div class="function-desc">Internal value for processing (e.g., "beginner", "intermediate", "advanced")</div>
        </li>
        <li>
          <div class="function-name">Option Order</div>
          <div class="function-desc">Display sequence within the question (1, 2, 3, etc.)</div>
        </li>
        <li>
          <div class="function-name">Plan Mapping</div>
          <div class="function-desc">Link specific options to workout/meal plans for automatic assignment</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>➕ Adding New Questions</h4>
      <ol class="step-list">
        <li><strong>Access Questionnaire Manager:</strong> Navigate from admin dashboard or use direct link above</li>
        <li><strong>Click "Add Question":</strong> Start creating a new question</li>
        <li><strong>Set Question Details:</strong> Enter display text, field name, and select question type</li>
        <li><strong>Configure Requirements:</strong> Set if question is required and affects plan assignment</li>
        <li><strong>Add Answer Options:</strong> For choice questions, add all possible answers with values</li>
        <li><strong>Set Display Order:</strong> Choose where the question appears in the sequence</li>
        <li><strong>Save and Test:</strong> Verify question appears correctly in the user questionnaire</li>
      </ol>
    </div>

    <div class="info-box">
      <h4>💡 Question Design Best Practices</h4>
      <p><strong>Clarity:</strong> Keep questions clear and concise. <strong>Language:</strong> Use simple language and avoid medical jargon. <strong>Testing:</strong> Test the questionnaire flow before deploying. <strong>Logic:</strong> Order questions logically from general to specific.</p>
    </div>
  </div>

  <div id="products" class="doc-section">
    <h2>🛒 Product Management</h2>
    <p>E-commerce integration for managing fitness products, supplements, and equipment.</p>

    <div class="quick-links">
      <h4>🔗 Product Management Links</h4>
      <a href="{% url 'acfit_admin:products-dashboard' %}" class="url-link" target="_blank">
        <i class="fas fa-shopping-cart"></i> Products Dashboard
      </a>
      <a href="/acfit-admin/products/product/" class="url-link" target="_blank">
        <i class="fas fa-box"></i> All Products
      </a>
      <a href="/acfit-admin/products/product/add/" class="url-link" target="_blank">
        <i class="fas fa-plus-circle"></i> Add New Product
      </a>
    </div>

    <div class="admin-section">
      <h4>📦 Product Information Management</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Product Name & Description</div>
          <div class="function-desc">Clear, descriptive product titles and detailed information about benefits, usage, and specifications.</div>
        </li>
        <li>
          <div class="function-name">Product Images</div>
          <div class="function-desc">High-quality product photos, multiple angles, lifestyle images showing product in use.</div>
        </li>
        <li>
          <div class="function-name">Pricing & Availability</div>
          <div class="function-desc">Current pricing, discount information, stock status, and availability updates.</div>
        </li>
        <li>
          <div class="function-name">Purchase Links</div>
          <div class="function-desc">External purchase URLs, affiliate links, and tracking codes for commission monitoring.</div>
        </li>
        <li>
          <div class="function-name">SEO Optimization</div>
          <div class="function-desc">Product slugs, meta descriptions, and keywords for better search visibility.</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>🏷️ Product Categories</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Supplements</div>
          <div class="function-desc">Protein powders, vitamins, pre-workouts, post-workout recovery, fat burners, and health supplements.</div>
        </li>
        <li>
          <div class="function-name">Equipment</div>
          <div class="function-desc">Home gym equipment, resistance bands, weights, yoga mats, and workout accessories.</div>
        </li>
        <li>
          <div class="function-name">Apparel</div>
          <div class="function-desc">Workout clothing, athletic shoes, fitness trackers, and performance gear.</div>
        </li>
        <li>
          <div class="function-name">Nutrition</div>
          <div class="function-desc">Healthy food products, meal kits, protein bars, and nutritional supplements.</div>
        </li>
      </ul>
    </div>

    <div class="success-box">
      <h4>✅ Product Strategy Best Practices</h4>
      <p><strong>Relevance:</strong> Focus on products that complement your workout and meal plans. <strong>Quality:</strong> Only recommend products you trust. <strong>Links:</strong> Ensure all product links are current and functional. <strong>Reviews:</strong> Monitor user feedback and ratings.</p>
    </div>
  </div>

  <div id="support" class="doc-section">
    <h2>🎧 Support Management</h2>
    <p>Customer support system for handling user questions, feedback, and issues.</p>

    <div class="quick-links">
      <h4>🔗 Support Management Links</h4>
      <a href="{% url 'acfit_admin:feedback' %}" class="url-link" target="_blank">
        <i class="fas fa-comments"></i> User Feedback
      </a>
      <a href="{% url 'acfit_admin:faqs' %}" class="url-link" target="_blank">
        <i class="fas fa-question-circle"></i> Manage FAQs
      </a>
      <a href="/acfit-admin/support/faq/" class="url-link" target="_blank">
        <i class="fas fa-list"></i> All FAQs
      </a>
      <a href="/acfit-admin/support/userfeedback/" class="url-link" target="_blank">
        <i class="fas fa-inbox"></i> All Feedback
      </a>
    </div>

    <div class="admin-section">
      <h4>❓ FAQ Management</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Question Categories</div>
          <div class="function-desc">Organize FAQs by topic: workouts, nutrition, technical issues, account management, billing, etc.</div>
        </li>
        <li>
          <div class="function-name">Answer Quality</div>
          <div class="function-desc">Provide clear, helpful responses with step-by-step instructions and relevant links.</div>
        </li>
        <li>
          <div class="function-name">Search Optimization</div>
          <div class="function-desc">Use keywords users might search for. Include common variations and synonyms.</div>
        </li>
        <li>
          <div class="function-name">Content Updates</div>
          <div class="function-desc">Keep information current and accurate. Review and update FAQs regularly.</div>
        </li>
        <li>
          <div class="function-name">Display Order</div>
          <div class="function-desc">Prioritize most common questions. Use order field to control FAQ sequence.</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>💬 User Feedback System</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Feedback Types</div>
          <div class="function-desc">Bug reports, feature requests, general feedback, complaints, and suggestions.</div>
        </li>
        <li>
          <div class="function-name">Priority Classification</div>
          <div class="function-desc">Urgent (system down), High (major issues), Medium (minor issues), Low (suggestions).</div>
        </li>
        <li>
          <div class="function-name">Status Tracking</div>
          <div class="function-desc">New, In Progress, Resolved, Closed. Track feedback through entire lifecycle.</div>
        </li>
        <li>
          <div class="function-name">Response Management</div>
          <div class="function-desc">Track admin responses, resolution times, and user satisfaction ratings.</div>
        </li>
        <li>
          <div class="function-name">Follow-up System</div>
          <div class="function-desc">Automated reminders for pending feedback and follow-up communications.</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>🔄 Feedback Resolution Process</h4>
      <ol class="step-list">
        <li><strong>Review Feedback:</strong> Read and understand the user's issue, request, or concern</li>
        <li><strong>Categorize & Prioritize:</strong> Assign appropriate type and priority level</li>
        <li><strong>Investigate:</strong> Research the issue, reproduce bugs, or evaluate feature requests</li>
        <li><strong>Respond to User:</strong> Provide helpful, professional response with timeline</li>
        <li><strong>Implement Solution:</strong> Fix bugs, implement features, or provide workarounds</li>
        <li><strong>Mark as Resolved:</strong> Update status when issue is completely addressed</li>
        <li><strong>Follow Up:</strong> Ensure user satisfaction and gather additional feedback</li>
      </ol>
    </div>

    <div class="warning-box">
      <h4>⚠️ Response Time Goals</h4>
      <p><strong>Urgent:</strong> 4 hours, <strong>High:</strong> 24 hours, <strong>Medium:</strong> 72 hours, <strong>Low:</strong> 1 week. Quick responses improve user satisfaction and retention.</p>
    </div>
  </div>

  <div id="activity" class="doc-section">
    <h2>📊 Activity Logs</h2>
    <p>Comprehensive activity tracking system for monitoring user behavior and system usage.</p>

    <div class="quick-links">
      <h4>🔗 Activity Monitoring Links</h4>
      <a href="/admin/activity_logs/activitylog/" class="url-link" target="_blank">
        <i class="fas fa-chart-line"></i> All Activity Logs
      </a>
      <a href="{% url 'acfit_admin:dashboard' %}" class="url-link" target="_blank">
        <i class="fas fa-home"></i> Dashboard (Recent Activity)
      </a>
    </div>

    <div class="admin-section">
      <h4>📋 Activity Types Tracked</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Workout Activities</div>
          <div class="function-desc">Workout started, completed, session completed, workout scheduled, exercise logs</div>
        </li>
        <li>
          <div class="function-name">Meal Activities</div>
          <div class="function-desc">Meals completed, scheduled, hydration updates, nutrition tracking</div>
        </li>
        <li>
          <div class="function-name">User Activities</div>
          <div class="function-desc">Login, logout, profile updates, questionnaire completion, plan assignments</div>
        </li>
        <li>
          <div class="function-name">System Activities</div>
          <div class="function-desc">Plan assignments, data updates, admin actions, system maintenance</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>📝 Log Information Details</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">User Identification</div>
          <div class="function-desc">Which user performed the activity, including user profile and account details</div>
        </li>
        <li>
          <div class="function-name">Timestamp</div>
          <div class="function-desc">Exact date and time of activity with timezone information</div>
        </li>
        <li>
          <div class="function-name">Activity Description</div>
          <div class="function-desc">Detailed description of what happened, including context and results</div>
        </li>
        <li>
          <div class="function-name">Related Objects</div>
          <div class="function-desc">Links to workouts, meals, plans, or other relevant data objects</div>
        </li>
        <li>
          <div class="function-name">Metadata</div>
          <div class="function-desc">Additional context, device info, IP address, and technical details</div>
        </li>
      </ul>
    </div>

    <div class="admin-section">
      <h4>🔍 Using Activity Logs</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">User Behavior Analysis</div>
          <div class="function-desc">Understand how users interact with the platform, popular features, usage patterns</div>
        </li>
        <li>
          <div class="function-name">Troubleshooting</div>
          <div class="function-desc">Investigate issues, track down bugs, understand error sequences</div>
        </li>
        <li>
          <div class="function-name">Performance Monitoring</div>
          <div class="function-desc">Track system usage patterns, peak times, resource utilization</div>
        </li>
        <li>
          <div class="function-name">Compliance & Auditing</div>
          <div class="function-desc">Maintain audit trails for data changes, security monitoring, compliance reporting</div>
        </li>
        <li>
          <div class="function-name">User Engagement</div>
          <div class="function-desc">Track user engagement, identify inactive users, measure feature adoption</div>
        </li>
      </ul>
    </div>

    <div class="info-box">
      <h4>💡 Analytics Insights</h4>
      <p><strong>Popular Features:</strong> Identify most-used features. <strong>User Paths:</strong> Understand common user journeys. <strong>Drop-off Points:</strong> Find where users stop engaging. <strong>Optimization:</strong> Use data to improve user experience.</p>
    </div>
  </div>

  <div id="assignment" class="doc-section">
    <h2>📝 Plan Assignment</h2>
    <p>Intelligent system for assigning workout and meal plans to users based on their profiles and preferences.</p>

    <h3>Assignment Methods</h3>
    <ul>
      <li><strong>Manual Assignment:</strong> Directly assign specific plans to individual users</li>
      <li><strong>Bulk Assignment:</strong> Assign plans to multiple users at once</li>
      <li><strong>Automatic Assignment:</strong> System assigns plans based on questionnaire responses</li>
      <li><strong>Criteria-Based:</strong> Assign plans based on user characteristics</li>
    </ul>

    <h3>Assignment Criteria</h3>
    <ul>
      <li><strong>Fitness Level:</strong> Beginner, intermediate, advanced</li>
      <li><strong>Goals:</strong> Weight loss, muscle gain, endurance, strength</li>
      <li><strong>Location:</strong> Home, gym, outdoor workouts</li>
      <li><strong>Equipment:</strong> Available equipment and space</li>
      <li><strong>Health Conditions:</strong> Medical considerations and limitations</li>
      <li><strong>Demographics:</strong> Age group and gender preferences</li>
    </ul>

    <h3>Assignment Process</h3>
    <ol class="step-list">
      <li><strong>Access Assignment Tool:</strong> Navigate to "Assign Plans" from dashboard</li>
      <li><strong>Select Users:</strong> Choose individual users or user groups</li>
      <li><strong>Choose Plans:</strong> Select appropriate workout and meal plans</li>
      <li><strong>Set Schedule:</strong> Define start dates and duration</li>
      <li><strong>Review Assignment:</strong> Verify compatibility and settings</li>
      <li><strong>Execute Assignment:</strong> Apply plans to selected users</li>
    </ol>

    <h3>Sync Options</h3>
    <ul>
      <li><strong>No Sync:</strong> User plans remain independent of global plan changes</li>
      <li><strong>Sync Updates:</strong> User plans update when global plans are modified</li>
      <li><strong>Sync New Content:</strong> Only new content is synced to user plans</li>
    </ul>

    <div class="warning-box">
      <h4>⚠️ Assignment Considerations</h4>
      <p>Always verify plan compatibility with user health conditions and fitness levels before assignment. Consider user preferences and limitations.</p>
    </div>
  </div>

  <div id="calendar" class="doc-section">
    <h2>📅 Calendar Views</h2>
    <p>Visual calendar interfaces for managing workout and meal schedules across users and time periods.</p>

    <h3>Workout Calendar</h3>
    <ul>
      <li><strong>Monthly View:</strong> See workout schedules across entire months</li>
      <li><strong>User Selection:</strong> Filter by specific users or view all users</li>
      <li><strong>Plan Filtering:</strong> Focus on specific workout plans</li>
      <li><strong>Schedule Details:</strong> View workout days and rest days</li>
      <li><strong>Progress Tracking:</strong> See completed vs. scheduled workouts</li>
    </ul>

    <h3>Meal Calendar</h3>
    <ul>
      <li><strong>Daily Meal Plans:</strong> View complete daily nutrition schedules</li>
      <li><strong>Meal Timing:</strong> See breakfast, lunch, dinner, and snack timing</li>
      <li><strong>Nutritional Overview:</strong> Daily calorie and macro targets</li>
      <li><strong>Completion Status:</strong> Track meal completion rates</li>
    </ul>

    <h3>Calendar Features</h3>
    <ul>
      <li><strong>Navigation:</strong> Easy month-to-month navigation</li>
      <li><strong>Color Coding:</strong> Visual indicators for different plan types</li>
      <li><strong>Quick Actions:</strong> Direct links to edit schedules</li>
      <li><strong>Export Options:</strong> Generate printable schedules</li>
    </ul>

    <div class="success-box">
      <h4>✅ Calendar Benefits</h4>
      <p>Calendar views help identify scheduling conflicts, track user progress, and optimize plan timing for better user engagement.</p>
    </div>
  </div>

  <div id="security" class="doc-section">
    <h2>🔒 Security & Permissions</h2>
    <p>Comprehensive security system protecting user data and controlling admin access.</p>

    <h3>Admin Access Control</h3>
    <ul>
      <li><strong>Superuser Requirement:</strong> Only superusers can access the admin interface</li>
      <li><strong>Email Verification:</strong> Admin accounts require verified email addresses</li>
      <li><strong>Session Management:</strong> Automatic session timeout for security</li>
      <li><strong>Activity Logging:</strong> All admin actions are logged for audit trails</li>
    </ul>

    <h3>Data Protection</h3>
    <ul>
      <li><strong>User Privacy:</strong> Personal health information is protected</li>
      <li><strong>Data Encryption:</strong> Sensitive data is encrypted in storage</li>
      <li><strong>Access Logs:</strong> Track who accesses what data and when</li>
      <li><strong>Backup Security:</strong> Regular secure backups of all data</li>
    </ul>

    <h3>Permission Levels</h3>
    <ul>
      <li><strong>View Permissions:</strong> Read-only access to specific data</li>
      <li><strong>Edit Permissions:</strong> Modify existing records</li>
      <li><strong>Create Permissions:</strong> Add new records to the system</li>
      <li><strong>Delete Permissions:</strong> Remove records (with confirmation)</li>
    </ul>

    <h3>Security Best Practices</h3>
    <ol class="step-list">
      <li><strong>Strong Passwords:</strong> Use complex passwords for admin accounts</li>
      <li><strong>Regular Updates:</strong> Keep the system updated with security patches</li>
      <li><strong>Access Review:</strong> Regularly review who has admin access</li>
      <li><strong>Data Backup:</strong> Maintain regular, secure backups</li>
      <li><strong>Monitor Logs:</strong> Review activity logs for suspicious activity</li>
      <li><strong>User Training:</strong> Train all admins on security procedures</li>
    </ol>

    <div class="warning-box">
      <h4>⚠️ Security Alert</h4>
      <p>Never share admin credentials. Always log out when finished. Report any suspicious activity immediately.</p>
    </div>
  </div>

  <div class="doc-section" style="text-align: center; background: linear-gradient(135deg, #417690, #2c5282); color: white;">
    <h2>🎉 Congratulations!</h2>
    <p>You now have comprehensive knowledge of the AC-FIT admin system. Use this documentation as your reference guide for managing the platform effectively.</p>

    <div style="margin-top: 30px;">
      <h3>Quick Support</h3>
      <p>If you need additional help or have questions not covered in this documentation, please contact the development team.</p>
    </div>

    <div style="margin-top: 20px;">
      <a href="{% url 'acfit_admin:dashboard' %}" style="background: white; color: #417690; padding: 15px 30px; border-radius: 25px; text-decoration: none; font-weight: bold; display: inline-block; margin: 10px;">
        🏠 Return to Dashboard
      </a>
    </div>
  </div>

  <div id="assignment" class="doc-section">
    <h2>🧠 Plan Assignment Algorithm</h2>
    <p>AC-FIT uses a sophisticated scoring algorithm to automatically assign the best workout and meal plans to users based on their questionnaire responses. This system ensures users get personalized plans that match their health conditions, fitness goals, and preferences.</p>

    <div class="info-box">
      <h4>🎯 Algorithm Overview</h4>
      <p>The plan assignment system uses a weighted scoring approach with intelligent fallback mechanisms to ensure every user gets assigned appropriate plans, even with unique health condition combinations.</p>
    </div>

    <h3>📊 Scoring System</h3>
    <p>The algorithm assigns weighted scores to different user attributes, prioritizing critical health and safety factors:</p>

    <div class="admin-section">
      <h4>🏆 Scoring Weights (Higher = More Important)</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Critical Match (10 points)</div>
          <div class="function-desc">Health conditions, Gender - Exact matches for safety and appropriateness</div>
        </li>
        <li>
          <div class="function-name">High Priority (5 points)</div>
          <div class="function-desc">Fitness goal, Age group - Important for plan effectiveness</div>
        </li>
        <li>
          <div class="function-name">Medium Priority (3 points)</div>
          <div class="function-desc">Fitness level, Workout location, Dietary preferences - Affects plan suitability</div>
        </li>
        <li>
          <div class="function-name">Low Priority (1 point)</div>
          <div class="function-desc">Workout days, Equipment availability - Nice-to-have preferences</div>
        </li>
        <li>
          <div class="function-name">Disqualification (-1 points)</div>
          <div class="function-desc">Incompatible health conditions - Safety override</div>
        </li>
      </ul>
    </div>

    <h3>🔄 Fallback Mechanism</h3>
    <p>When no plans match strict criteria (Score: -1), the system uses progressive fallback levels to ensure users always get assigned plans:</p>

    <div class="feature-grid">
      <div class="feature-card">
        <h4>💪 Workout Plan Fallbacks</h4>
        <ol class="step-list">
          <li><strong>Level 1:</strong> Match fitness_level + location + equipment (ignore health conditions)</li>
          <li><strong>Level 2:</strong> Match fitness_level only (ignore location and health conditions)</li>
          <li><strong>Level 3:</strong> Match gender only (basic compatibility)</li>
          <li><strong>Level 4:</strong> Any available plan (absolute last resort)</li>
        </ol>
      </div>
      <div class="feature-card">
        <h4>🍽️ Meal Plan Fallbacks</h4>
        <ol class="step-list">
          <li><strong>Level 1:</strong> Match gender + dietary preferences (ignore health conditions)</li>
          <li><strong>Level 2:</strong> Match gender only (ignore dietary preferences and health conditions)</li>
          <li><strong>Level 3:</strong> Match dietary preferences only (ignore gender and health conditions)</li>
          <li><strong>Level 4:</strong> Any available plan (absolute last resort)</li>
        </ol>
      </div>
    </div>

    <h3>⚙️ Algorithm Flow</h3>
    <div class="admin-section">
      <h4>🔍 Step-by-Step Process</h4>
      <ol class="step-list">
        <li><strong>User Questionnaire:</strong> Collect user profile data (health, goals, preferences)</li>
        <li><strong>Plan Scoring:</strong> Calculate weighted scores for all available plans</li>
        <li><strong>Best Match Selection:</strong> Choose plans with highest scores (if score > -1)</li>
        <li><strong>Fallback Activation:</strong> If no matches found, use progressive fallback levels</li>
        <li><strong>Plan Assignment:</strong> Create UserWorkoutPlan and UserMealPlan records</li>
        <li><strong>Content Cloning:</strong> Copy plan templates to user-specific schedules</li>
        <li><strong>Progress Tracking:</strong> Initialize user progress and scoring systems</li>
      </ol>
    </div>

    <h3>🏥 Health Condition Handling</h3>
    <div class="warning-box">
      <h4>⚠️ Safety First</h4>
      <p>The algorithm prioritizes user safety by strictly checking health condition compatibility. Plans that don't support a user's health conditions receive a disqualification score (-1).</p>
    </div>

    <div class="admin-section">
      <h4>🩺 Supported Health Conditions</h4>
      <ul>
        <li><strong>DIABETES:</strong> Plans with appropriate carb management</li>
        <li><strong>HIGH_CHOLESTEROL:</strong> Heart-healthy meal and exercise plans</li>
        <li><strong>HYPERTENSION:</strong> Low-sodium meals and appropriate exercise intensity</li>
        <li><strong>HEART_DISEASE:</strong> Cardiac-safe exercise protocols</li>
        <li><strong>ARTHRITIS:</strong> Joint-friendly workout modifications</li>
        <li><strong>NONE:</strong> Standard plans for healthy individuals</li>
      </ul>
    </div>

    <h3>📈 Algorithm Performance</h3>
    <div class="feature-grid">
      <div class="feature-card">
        <h4>✅ Strengths</h4>
        <ul>
          <li>Comprehensive user attribute matching</li>
          <li>Safety-first health condition checking</li>
          <li>Intelligent fallback mechanisms</li>
          <li>Weighted priority system</li>
          <li>100% assignment guarantee</li>
        </ul>
      </div>
      <div class="feature-card">
        <h4>🔧 Optimizations</h4>
        <ul>
          <li>Database indexing on frequently filtered fields</li>
          <li>Caching for plan metadata</li>
          <li>Batch processing for bulk assignments</li>
          <li>Logging for debugging and monitoring</li>
          <li>Progressive fallback to prevent failures</li>
        </ul>
      </div>
    </div>

    <h3>🛠️ Admin Tools</h3>
    <div class="quick-links">
      <h4>🔗 Plan Management Links</h4>
      <a href="{% url 'acfit_admin:assign-plans' %}" class="url-link" target="_blank">
        <i class="fas fa-user-plus"></i> Assign Plans
      </a>
      <a href="{% url 'acfit_admin:workout-plans' %}" class="url-link" target="_blank">
        <i class="fas fa-dumbbell"></i> Workout Plans
      </a>
      <a href="{% url 'acfit_admin:meal-plans' %}" class="url-link" target="_blank">
        <i class="fas fa-utensils"></i> Meal Plans
      </a>
      <a href="/acfit-admin/accounts/userprofile/" class="url-link" target="_blank">
        <i class="fas fa-user-circle"></i> User Profiles
      </a>
    </div>

    <div class="admin-section">
      <h4>🔍 Monitoring & Debugging</h4>
      <ul class="function-list">
        <li>
          <div class="function-name">Plan Assignment Logs</div>
          <div class="function-desc">Check server logs for "Plan Matching (Best Fit)" entries to see scoring details</div>
        </li>
        <li>
          <div class="function-name">Fallback Activation</div>
          <div class="function-desc">Look for "Fallback Level X" logs when strict matching fails</div>
        </li>
        <li>
          <div class="function-name">Score Analysis</div>
          <div class="function-desc">Monitor plan scores to identify which attributes are most important</div>
        </li>
        <li>
          <div class="function-name">Health Condition Coverage</div>
          <div class="function-desc">Ensure plans exist for all supported health conditions</div>
        </li>
      </ul>
    </div>

    <div class="code-block">
# Example Log Output:
INFO Plan Matching (Best Fit) - Profile: username
INFO Profile Details: Goal=LOSE_WEIGHT, Level=INTERMEDIATE, Gender=M,
     AgeGroup=UNDER_30, Location=GYM, HealthConditions=['DIABETES']
INFO Plan Matching (Best Fit) - Selected Workout Plan: Diabetic Workout (Score: 27)
INFO Plan Matching (Best Fit) - Selected Meal Plan: Low-Carb Meal Plan (Score: 23)
    </div>

    <div class="success-box">
      <h4>🎯 Best Practices</h4>
      <ul>
        <li>Create plans for common health condition combinations</li>
        <li>Use "UNISEX" gender for plans suitable for all users</li>
        <li>Set health_conditions_allowed to ['NONE'] for general plans</li>
        <li>Monitor fallback activation rates to identify missing plan types</li>
        <li>Regularly review plan assignment logs for optimization opportunities</li>
      </ul>
    </div>
  </div>
</div>

<a href="#" class="back-to-top">↑ Top</a>

<script>
// Smooth scrolling for navigation links
document.querySelectorAll('.doc-nav a').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Back to top functionality
document.querySelector('.back-to-top').addEventListener('click', function(e) {
    e.preventDefault();
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});

// Show/hide back to top button based on scroll position
window.addEventListener('scroll', function() {
    const backToTop = document.querySelector('.back-to-top');
    if (window.pageYOffset > 300) {
        backToTop.style.display = 'block';
    } else {
        backToTop.style.display = 'none';
    }
});
</script>

{% endblock %}
