from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.contrib.auth import get_user_model
from django.template.loader import render_to_string
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from .verification import VerificationCode
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

class SendVerificationCodeView(APIView):
    """
    API view to send verification codes for email verification or password reset.
    """
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get('email')
        code_type = request.data.get('code_type', 'email')  # Default to email verification

        if not email:
            return Response({'error': 'Email is required'}, status=status.HTTP_400_BAD_REQUEST)

        if code_type not in ['email', 'password']:
            return Response({'error': 'Invalid code type'}, status=status.HTTP_400_BAD_REQUEST)

        # Check if user exists
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            if code_type == 'email':
                return Response({'error': 'User with this email does not exist'}, status=status.HTTP_404_NOT_FOUND)
            else:
                # For password reset, don't reveal if user exists for security
                return Response({'detail': 'If an account with this email exists, a verification code has been sent.'},
                               status=status.HTTP_200_OK)

        # For staff accounts, skip email verification
        if code_type == 'email' and (user.is_staff or user.is_superuser):
            return Response({
                'detail': 'Staff accounts do not require email verification.',
                'is_staff': True
            }, status=status.HTTP_200_OK)

        # Generate verification code
        verification_code = VerificationCode.create_verification_code(email, code_type)

        # Send email with verification code
        try:
            context = {
                'user': user,
                'code': verification_code.code,
                'code_type': code_type,
                'date': timezone.now(),
                'expiry_hours': 1  # Code expires in 1 hour
            }

            subject = 'Email Verification Code' if code_type == 'email' else 'Password Reset Code'
            email_html_message = render_to_string('emails/verification_code_email.html', context)

            # Log the code and email settings for debugging purposes
            logger.info(f"Sending {code_type} verification code {verification_code.code} to {email}")
            logger.info(f"Email settings: HOST={settings.EMAIL_HOST}, PORT={settings.EMAIL_PORT}")
            logger.info(f"Email USER={settings.EMAIL_HOST_USER}, FROM={settings.DEFAULT_FROM_EMAIL}")
            logger.info(f"Email SSL={settings.EMAIL_USE_SSL}, TLS={settings.EMAIL_USE_TLS}")

            # Check if we should bypass sending actual emails
            if settings.BYPASS_EMAIL_SENDING:
                logger.warning(f"Email sending bypassed in debug mode. Verification code: {verification_code.code}")
                logger.info(f"DEBUG MODE: {code_type.capitalize()} verification code for {email}: {verification_code.code}")
            else:
                # Send the email with detailed error handling
                try:
                    send_result = send_mail(
                        subject=subject,
                        message=f"Your verification code is: {verification_code.code}",  # Plain text fallback
                        html_message=email_html_message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[email],
                        fail_silently=False,
                    )
                    logger.info(f"Email send result: {send_result}")
                    logger.info(f"{code_type.capitalize()} verification code sent to {email}")
                except Exception as email_error:
                    logger.error(f"Email sending error: {str(email_error)}")
                    if settings.DEBUG:
                        # In debug mode, log the error but don't raise an exception
                        logger.warning(f"Email sending failed, but continuing in debug mode. Code: {verification_code.code}")
                        # Print the code prominently in the console for easy visibility
                        print("\n" + "=" * 80)
                        print(f"DEBUG MODE (EMAIL ERROR): {code_type.capitalize()} VERIFICATION CODE: {verification_code.code}")
                        print("=" * 80 + "\n")
                    else:
                        # In production, re-raise the exception
                        raise

            # Always include the verification code in the response
            response_data = {
                'detail': f'Verification code has been sent to {email}',
                'email': email,
                'code_type': code_type,
                'verification_code': verification_code.code  # Always include the code
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Failed to send verification code to {email}: {str(e)}")
            return Response({'error': 'Failed to send verification code'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class VerifyEmailCodeView(APIView):
    """
    API view to verify email verification codes.
    """
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get('email')
        code = request.data.get('code')

        if not email or not code:
            return Response({'error': 'Email and code are required'}, status=status.HTTP_400_BAD_REQUEST)

        # Verify the code
        if VerificationCode.verify_code(email, code, 'email'):
            try:
                user = User.objects.get(email=email)

                # Mark user as active (verified)
                if not user.is_active:
                    user.is_active = True
                    user.save()

                # Generate JWT tokens for automatic login
                from rest_framework_simplejwt.tokens import RefreshToken
                refresh = RefreshToken.for_user(user)

                return Response({
                    'detail': 'Email verified successfully',
                    'refresh': str(refresh),
                    'access': str(refresh.access_token),
                    'user': {
                        'email': user.email,
                        'username': user.username,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'is_staff': user.is_staff,
                        'is_superuser': user.is_superuser
                    }
                }, status=status.HTTP_200_OK)

            except User.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
        else:
            return Response({'error': 'Invalid or expired verification code'}, status=status.HTTP_400_BAD_REQUEST)


class VerifyPasswordResetCodeView(APIView):
    """
    API view to verify password reset codes.
    """
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get('email')
        code = request.data.get('code')

        if not email or not code:
            return Response({'error': 'Email and code are required'}, status=status.HTTP_400_BAD_REQUEST)

        # Verify the code
        if VerificationCode.verify_code(email, code, 'password'):
            try:
                user = User.objects.get(email=email)

                return Response({
                    'detail': 'Password reset code verified successfully',
                    'email': email,
                    'verified': True
                }, status=status.HTTP_200_OK)

            except User.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
        else:
            return Response({'error': 'Invalid or expired verification code'}, status=status.HTTP_400_BAD_REQUEST)


class ResetPasswordWithCodeView(APIView):
    """
    API view to reset password using a verified code.
    """
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get('email')
        code = request.data.get('code')
        new_password1 = request.data.get('new_password1')
        new_password2 = request.data.get('new_password2')

        if not email or not code or not new_password1 or not new_password2:
            return Response({'error': 'Email, code, and new password are required'}, status=status.HTTP_400_BAD_REQUEST)

        if new_password1 != new_password2:
            return Response({'error': 'Passwords do not match'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get the verification code from the database directly
            verification_code = VerificationCode.objects.get(
                email=email,
                code=code,
                code_type='password',
                is_used=False
            )

            # Check if the code is still valid
            if not verification_code.is_valid():
                return Response({'error': 'Verification code has expired'}, status=status.HTTP_400_BAD_REQUEST)

            # Mark the code as used
            verification_code.is_used = True
            verification_code.save()

            # Get the user and reset password
            try:
                user = User.objects.get(email=email)

                # Set new password
                user.set_password(new_password1)
                user.save()

                # Generate JWT tokens for automatic login
                from rest_framework_simplejwt.tokens import RefreshToken
                refresh = RefreshToken.for_user(user)

                return Response({
                    'detail': 'Password reset successfully',
                    'refresh': str(refresh),
                    'access': str(refresh.access_token),
                    'user': {
                        'email': user.email,
                        'username': user.username,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'is_staff': user.is_staff,
                        'is_superuser': user.is_superuser
                    }
                }, status=status.HTTP_200_OK)

            except User.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

        except VerificationCode.DoesNotExist:
            return Response({'error': 'Invalid or expired verification code'}, status=status.HTTP_400_BAD_REQUEST)


class GetLatestVerificationCodeView(APIView):
    """
    API view to get the latest verification code for development purposes.
    This should only be used in development/debug mode.
    """
    permission_classes = [AllowAny]

    def get(self, request):
        email = request.query_params.get('email')
        code_type = request.query_params.get('code_type')

        if not email or not code_type:
            return Response({'error': 'Email and code_type are required'}, status=status.HTTP_400_BAD_REQUEST)

        # Only allow this in debug mode for security
        if not settings.DEBUG:
            return Response({'error': 'This endpoint is only available in debug mode'}, status=status.HTTP_403_FORBIDDEN)

        try:
            # Get the latest unused verification code for this email and type
            verification_code = VerificationCode.objects.filter(
                email=email,
                code_type=code_type,
                is_used=False
            ).order_by('-created_at').first()

            if verification_code and verification_code.is_valid():
                return Response({
                    'code': verification_code.code,
                    'email': email,
                    'code_type': code_type,
                    'expires_at': verification_code.expires_at
                }, status=status.HTTP_200_OK)
            else:
                return Response({'error': 'No valid verification code found'}, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            logger.error(f"Failed to get latest verification code for {email}: {str(e)}")
            return Response({'error': 'Failed to get verification code'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
