name: acfit_app
description: A new Flutter project.
publish_to: 'none'
version: 0.1.0

environment:
  sdk: '>=3.1.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_timezone: ^4.1.0
  image_picker: ^1.0.7
  flutter_secure_storage: ^8.0.0
  shared_preferences: ^2.2.0 # Used in CacheService
  provider: ^6.0.5
  connectivity_plus: ^5.0.2
  cupertino_icons: ^1.0.2
  flutter_svg: ^2.0.7
  intl: 0.20.2
  fl_chart: ^0.65.0
  shimmer: ^3.0.0
  cached_network_image: ^3.3.0
  video_player: ^2.8.2
  gif: ^2.3.0 # Added for better GIF handling
  http: ^1.1.0 # Added for GIF downloading
  json_annotation: ^4.8.1
  dio: ^5.4.0
  http_parser: ^4.0.2 # Added for MediaType in profile picture upload
  table_calendar: ^3.0.9
  url_launcher: ^6.2.1 # Added for launching URLs/email
  wakelock_plus: ^1.1.4 # Added to keep screen on during workouts
  timezone: ^0.10.0
  flutter_local_notifications: ^19.1.0 # Use newer version compatible with timezone ^0.10.0
  audioplayers: ^5.2.1 # Added for sound effects
  workmanager: ^0.5.2 # For background tasks

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
  flutter_lints: ^2.0.0

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/social/
    - assets/images/home/
    - assets/icons/
    - assets/sounds/

  # Enable generation of localized strings
  generate: true
