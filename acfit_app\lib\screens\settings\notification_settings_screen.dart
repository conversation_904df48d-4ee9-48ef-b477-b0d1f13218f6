import 'package:flutter/material.dart';
import '../../models/notification_settings.dart';
import '../../services/notification_api_service.dart';
import '../../utils/logger.dart';
import '../../widgets/common/animated_loading.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationSettingsScreen> createState() =>
      _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState
    extends State<NotificationSettingsScreen> {
  NotificationSettings? _settings;
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      setState(() => _isLoading = true);

      final settings = await NotificationApiService.getNotificationSettings();

      setState(() {
        _settings = settings ??
            NotificationSettings(
              workoutRemindersEnabled: true,
              mealRemindersEnabled: true,
              hydrationRemindersEnabled: true,
              weeklyMotivationEnabled: true,
              adminMessagesEnabled: true,
              hydrationIntervalHours: 2,
              workoutReminderMinutes: 30,
              mealReminderMinutes: 15,
              weeklyMotivationDay: 1,
              weeklyMotivationHour: 9,
            );
        _isLoading = false;
      });
    } catch (e) {
      Logger.log('Error loading notification settings: $e',
          tag: 'NotificationSettingsScreen');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveSettings() async {
    if (_settings == null) return;

    try {
      setState(() => _isSaving = true);

      final updatedSettings =
          await NotificationApiService.updateNotificationSettings(_settings!);

      if (updatedSettings != null) {
        setState(() => _settings = updatedSettings);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Settings saved successfully')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to save settings')),
          );
        }
      }
    } catch (e) {
      Logger.log('Error saving notification settings: $e',
          tag: 'NotificationSettingsScreen');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error saving settings')),
        );
      }
    } finally {
      setState(() => _isSaving = false);
    }
  }

  void _updateSettings(NotificationSettings newSettings) {
    setState(() => _settings = newSettings);
    _saveSettings();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const AnimatedLoading()
          : _settings == null
              ? _buildErrorState()
              : _buildSettingsContent(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'Failed to load settings',
            style: TextStyle(fontSize: 18),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadSettings,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsContent() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSection(
          'Reminder Types',
          [
            _buildSwitchTile(
              title: 'Workout Reminders',
              subtitle: 'Get notified before your workouts',
              icon: Icons.fitness_center,
              value: _settings!.workoutRemindersEnabled,
              onChanged: (value) => _updateSettings(
                _settings!.copyWith(workoutRemindersEnabled: value),
              ),
            ),
            _buildSwitchTile(
              title: 'Meal Reminders',
              subtitle: 'Get notified before meal times',
              icon: Icons.restaurant,
              value: _settings!.mealRemindersEnabled,
              onChanged: (value) => _updateSettings(
                _settings!.copyWith(mealRemindersEnabled: value),
              ),
            ),
            _buildSwitchTile(
              title: 'Hydration Reminders',
              subtitle: 'Regular reminders to drink water',
              icon: Icons.water_drop,
              value: _settings!.hydrationRemindersEnabled,
              onChanged: (value) => _updateSettings(
                _settings!.copyWith(hydrationRemindersEnabled: value),
              ),
            ),
            _buildSwitchTile(
              title: 'Weekly Motivation',
              subtitle: 'Motivational messages to keep you going',
              icon: Icons.star,
              value: _settings!.weeklyMotivationEnabled,
              onChanged: (value) => _updateSettings(
                _settings!.copyWith(weeklyMotivationEnabled: value),
              ),
            ),
            _buildSwitchTile(
              title: 'Admin Messages',
              subtitle: 'Important updates and announcements',
              icon: Icons.campaign,
              value: _settings!.adminMessagesEnabled,
              onChanged: (value) => _updateSettings(
                _settings!.copyWith(adminMessagesEnabled: value),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
        _buildSection(
          'Timing Settings',
          [
            _buildSliderTile(
              title: 'Hydration Interval',
              subtitle: 'Hours between hydration reminders',
              value: _settings!.hydrationIntervalHours.toDouble(),
              min: 1,
              max: 12,
              divisions: 11,
              label: '${_settings!.hydrationIntervalHours} hours',
              onChanged: _settings!.hydrationRemindersEnabled
                  ? (value) => _updateSettings(
                        _settings!
                            .copyWith(hydrationIntervalHours: value.round()),
                      )
                  : null,
            ),
            _buildSliderTile(
              title: 'Workout Reminder',
              subtitle: 'Minutes before workout to remind',
              value: _settings!.workoutReminderMinutes.toDouble(),
              min: 5,
              max: 120,
              divisions: 23,
              label: '${_settings!.workoutReminderMinutes} minutes',
              onChanged: _settings!.workoutRemindersEnabled
                  ? (value) => _updateSettings(
                        _settings!
                            .copyWith(workoutReminderMinutes: value.round()),
                      )
                  : null,
            ),
            _buildSliderTile(
              title: 'Meal Reminder',
              subtitle: 'Minutes before meal to remind',
              value: _settings!.mealReminderMinutes.toDouble(),
              min: 5,
              max: 60,
              divisions: 11,
              label: '${_settings!.mealReminderMinutes} minutes',
              onChanged: _settings!.mealRemindersEnabled
                  ? (value) => _updateSettings(
                        _settings!.copyWith(mealReminderMinutes: value.round()),
                      )
                  : null,
            ),
          ],
        ),
        const SizedBox(height: 24),
        _buildSection(
          'Weekly Motivation',
          [
            ListTile(
              leading: const Icon(Icons.calendar_today),
              title: const Text('Day of Week'),
              subtitle: Text(_settings!.weeklyMotivationDayName),
              trailing: const Icon(Icons.chevron_right),
              enabled: _settings!.weeklyMotivationEnabled,
              onTap: _settings!.weeklyMotivationEnabled ? _showDayPicker : null,
            ),
            ListTile(
              leading: const Icon(Icons.access_time),
              title: const Text('Time'),
              subtitle: Text(_settings!.weeklyMotivationTimeString),
              trailing: const Icon(Icons.chevron_right),
              enabled: _settings!.weeklyMotivationEnabled,
              onTap:
                  _settings!.weeklyMotivationEnabled ? _showTimePicker : null,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 8),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
        Card(
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      secondary: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
    );
  }

  Widget _buildSliderTile({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required String label,
    ValueChanged<double>? onChanged,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(subtitle),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            label: label,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  void _showDayPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Day'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(7, (index) {
            const days = [
              'Monday',
              'Tuesday',
              'Wednesday',
              'Thursday',
              'Friday',
              'Saturday',
              'Sunday'
            ];
            return RadioListTile<int>(
              title: Text(days[index]),
              value: index,
              groupValue: _settings!.weeklyMotivationDay,
              onChanged: (value) {
                if (value != null) {
                  _updateSettings(
                      _settings!.copyWith(weeklyMotivationDay: value));
                  Navigator.pop(context);
                }
              },
            );
          }),
        ),
      ),
    );
  }

  void _showTimePicker() async {
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(hour: _settings!.weeklyMotivationHour, minute: 0),
    );

    if (time != null) {
      _updateSettings(_settings!.copyWith(weeklyMotivationHour: time.hour));
    }
  }
}
