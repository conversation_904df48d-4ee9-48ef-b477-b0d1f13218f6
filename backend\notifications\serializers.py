from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    NotificationLog, UserNotificationSettings, 
    AdminNotificationCampaign, NotificationTemplate
)
from workouts.models import WorkoutPlan
from meals.models import MealPlan

User = get_user_model()


class NotificationLogSerializer(serializers.ModelSerializer):
    """Serializer for notification logs"""
    
    class Meta:
        model = NotificationLog
        fields = [
            'id', 'notification_type', 'title', 'message', 
            'status', 'sent_at', 'read_at', 'created_at', 'metadata'
        ]
        read_only_fields = ['id', 'sent_at', 'created_at']


class UserNotificationSettingsSerializer(serializers.ModelSerializer):
    """Serializer for user notification settings"""
    
    class Meta:
        model = UserNotificationSettings
        fields = [
            'workout_reminders_enabled', 'meal_reminders_enabled',
            'hydration_reminders_enabled', 'weekly_motivation_enabled',
            'admin_messages_enabled', 'hydration_interval_hours',
            'workout_reminder_minutes', 'meal_reminder_minutes',
            'weekly_motivation_day', 'weekly_motivation_hour'
        ]
    
    def validate_hydration_interval_hours(self, value):
        if value < 1 or value > 12:
            raise serializers.ValidationError("Hydration interval must be between 1 and 12 hours")
        return value
    
    def validate_workout_reminder_minutes(self, value):
        if value < 5 or value > 120:
            raise serializers.ValidationError("Workout reminder must be between 5 and 120 minutes")
        return value
    
    def validate_meal_reminder_minutes(self, value):
        if value < 5 or value > 60:
            raise serializers.ValidationError("Meal reminder must be between 5 and 60 minutes")
        return value
    
    def validate_weekly_motivation_day(self, value):
        if value < 0 or value > 6:
            raise serializers.ValidationError("Day must be between 0 (Monday) and 6 (Sunday)")
        return value
    
    def validate_weekly_motivation_hour(self, value):
        if value < 0 or value > 23:
            raise serializers.ValidationError("Hour must be between 0 and 23")
        return value


class AdminNotificationCampaignSerializer(serializers.ModelSerializer):
    """Serializer for admin notification campaigns"""
    created_by_email = serializers.CharField(source='created_by.email', read_only=True)
    target_workout_plans = serializers.SerializerMethodField()
    target_meal_plans = serializers.SerializerMethodField()
    
    class Meta:
        model = AdminNotificationCampaign
        fields = [
            'id', 'title', 'message', 'target_type',
            'target_workout_plan_ids', 'target_meal_plan_ids',
            'target_questionnaire_criteria', 'target_user_ids',
            'total_recipients', 'sent_count', 'failed_count',
            'is_sent', 'sent_at', 'created_at', 'created_by_email',
            'target_workout_plans', 'target_meal_plans'
        ]
        read_only_fields = [
            'id', 'total_recipients', 'sent_count', 'failed_count',
            'is_sent', 'sent_at', 'created_at', 'created_by_email'
        ]
    
    def get_target_workout_plans(self, obj):
        if obj.target_workout_plan_ids:
            plans = WorkoutPlan.objects.filter(id__in=obj.target_workout_plan_ids)
            return [{'id': plan.id, 'name': plan.name} for plan in plans]
        return []
    
    def get_target_meal_plans(self, obj):
        if obj.target_meal_plan_ids:
            plans = MealPlan.objects.filter(id__in=obj.target_meal_plan_ids)
            return [{'id': plan.id, 'name': plan.name} for plan in plans]
        return []


class CreateAdminNotificationSerializer(serializers.Serializer):
    """Serializer for creating admin notifications"""
    title = serializers.CharField(max_length=100)
    message = serializers.CharField()
    target_type = serializers.ChoiceField(choices=AdminNotificationCampaign.TARGET_TYPES)
    target_workout_plan_ids = serializers.ListField(
        child=serializers.IntegerField(), required=False, default=list
    )
    target_meal_plan_ids = serializers.ListField(
        child=serializers.IntegerField(), required=False, default=list
    )
    target_questionnaire_criteria = serializers.JSONField(required=False, default=dict)
    target_user_ids = serializers.ListField(
        child=serializers.CharField(), required=False, default=list
    )
    
    def validate(self, data):
        target_type = data.get('target_type')
        
        if target_type == 'WORKOUT_PLAN' and not data.get('target_workout_plan_ids'):
            raise serializers.ValidationError("Workout plan IDs are required for workout plan targeting")
        
        if target_type == 'MEAL_PLAN' and not data.get('target_meal_plan_ids'):
            raise serializers.ValidationError("Meal plan IDs are required for meal plan targeting")
        
        if target_type == 'QUESTIONNAIRE' and not data.get('target_questionnaire_criteria'):
            raise serializers.ValidationError("Questionnaire criteria are required for questionnaire targeting")
        
        if target_type == 'CUSTOM' and not data.get('target_user_ids'):
            raise serializers.ValidationError("User IDs are required for custom targeting")
        
        return data


class NotificationTemplateSerializer(serializers.ModelSerializer):
    """Serializer for notification templates"""
    
    class Meta:
        model = NotificationTemplate
        fields = ['id', 'notification_type', 'title', 'message', 'is_active', 'created_at']
        read_only_fields = ['id', 'created_at']


class MarkAsReadSerializer(serializers.Serializer):
    """Serializer for marking notifications as read"""
    notification_ids = serializers.ListField(
        child=serializers.CharField(),
        help_text="List of notification IDs to mark as read"
    )
