# Generated by Django 5.2.1 on 2025-06-01 14:48

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('notification_type', models.CharField(choices=[('WORKOUT_REMINDER', 'Workout Reminder'), ('MEAL_REMINDER', 'Meal Reminder'), ('HYDRATION_REMINDER', 'Hydration Reminder'), ('WEEKLY_MOTIVATION', 'Weekly Motivation'), ('ADMIN_MESSAGE', 'Admin Message')], max_length=20)),
                ('title', models.Char<PERSON>ield(max_length=100)),
                ('message', models.TextField()),
                ('is_active', models.<PERSON><PERSON>anField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['notification_type', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='AdminNotificationCampaign',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=100)),
                ('message', models.TextField()),
                ('target_type', models.CharField(choices=[('ALL_USERS', 'All Users'), ('WORKOUT_PLAN', 'Users with Specific Workout Plan'), ('MEAL_PLAN', 'Users with Specific Meal Plan'), ('QUESTIONNAIRE', 'Users with Specific Questionnaire Answers'), ('CUSTOM', 'Custom User Selection')], max_length=20)),
                ('target_workout_plan_ids', models.JSONField(blank=True, default=list)),
                ('target_meal_plan_ids', models.JSONField(blank=True, default=list)),
                ('target_questionnaire_criteria', models.JSONField(blank=True, default=dict)),
                ('target_user_ids', models.JSONField(blank=True, default=list)),
                ('total_recipients', models.IntegerField(default=0)),
                ('sent_count', models.IntegerField(default=0)),
                ('failed_count', models.IntegerField(default=0)),
                ('is_sent', models.BooleanField(default=False)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_campaigns', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserNotificationSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('workout_reminders_enabled', models.BooleanField(default=True)),
                ('meal_reminders_enabled', models.BooleanField(default=True)),
                ('hydration_reminders_enabled', models.BooleanField(default=True)),
                ('weekly_motivation_enabled', models.BooleanField(default=True)),
                ('admin_messages_enabled', models.BooleanField(default=True)),
                ('hydration_interval_hours', models.IntegerField(default=2, help_text='Hours between hydration reminders')),
                ('workout_reminder_minutes', models.IntegerField(default=30, help_text='Minutes before workout to send reminder')),
                ('meal_reminder_minutes', models.IntegerField(default=15, help_text='Minutes before meal to send reminder')),
                ('weekly_motivation_day', models.IntegerField(default=1, help_text='Day of week for motivation (0=Monday)')),
                ('weekly_motivation_hour', models.IntegerField(default=9, help_text='Hour of day for motivation (24h format)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_settings', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='NotificationLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('notification_type', models.CharField(choices=[('WORKOUT_REMINDER', 'Workout Reminder'), ('MEAL_REMINDER', 'Meal Reminder'), ('HYDRATION_REMINDER', 'Hydration Reminder'), ('WEEKLY_MOTIVATION', 'Weekly Motivation'), ('ADMIN_MESSAGE', 'Admin Message')], max_length=20)),
                ('title', models.CharField(max_length=100)),
                ('message', models.TextField()),
                ('admin_message_id', models.CharField(blank=True, help_text="Admin's custom message ID", max_length=50, null=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('SENT', 'Sent'), ('FAILED', 'Failed'), ('READ', 'Read')], default='PENDING', max_length=10)),
                ('scheduled_for', models.DateTimeField(blank=True, null=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='Additional data like workout_id, meal_id, etc.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('sent_by_admin', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_notifications', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notification_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'notification_type'], name='notificatio_user_id_46a8ea_idx'), models.Index(fields=['status', 'scheduled_for'], name='notificatio_status_2e04f4_idx')],
            },
        ),
    ]
