from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    NotificationLog, UserNotificationSettings, 
    AdminNotificationCampaign, NotificationTemplate
)


@admin.register(NotificationLog)
class NotificationLogAdmin(admin.ModelAdmin):
    list_display = ['user_email', 'notification_type', 'title', 'status', 'sent_at', 'created_at']
    list_filter = ['notification_type', 'status', 'created_at']
    search_fields = ['user__email', 'title', 'message']
    readonly_fields = ['id', 'created_at', 'sent_at', 'read_at']
    
    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'User Email'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(UserNotificationSettings)
class UserNotificationSettingsAdmin(admin.ModelAdmin):
    list_display = ['user_email', 'workout_enabled', 'meal_enabled', 'hydration_enabled', 'motivation_enabled']
    list_filter = ['workout_reminders_enabled', 'meal_reminders_enabled', 'hydration_reminders_enabled']
    search_fields = ['user__email']
    
    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'User Email'
    
    def workout_enabled(self, obj):
        return obj.workout_reminders_enabled
    workout_enabled.boolean = True
    workout_enabled.short_description = 'Workout'
    
    def meal_enabled(self, obj):
        return obj.meal_reminders_enabled
    meal_enabled.boolean = True
    meal_enabled.short_description = 'Meal'
    
    def hydration_enabled(self, obj):
        return obj.hydration_reminders_enabled
    hydration_enabled.boolean = True
    hydration_enabled.short_description = 'Hydration'
    
    def motivation_enabled(self, obj):
        return obj.weekly_motivation_enabled
    motivation_enabled.boolean = True
    motivation_enabled.short_description = 'Motivation'


@admin.register(AdminNotificationCampaign)
class AdminNotificationCampaignAdmin(admin.ModelAdmin):
    list_display = ['title', 'target_type', 'created_by_email', 'total_recipients', 'sent_count', 'is_sent', 'created_at']
    list_filter = ['target_type', 'is_sent', 'created_at']
    search_fields = ['title', 'message', 'created_by__email']
    readonly_fields = ['id', 'total_recipients', 'sent_count', 'failed_count', 'is_sent', 'sent_at', 'created_at']
    
    fieldsets = (
        ('Campaign Details', {
            'fields': ('title', 'message', 'target_type')
        }),
        ('Targeting', {
            'fields': ('target_workout_plan_ids', 'target_meal_plan_ids', 'target_questionnaire_criteria', 'target_user_ids'),
            'classes': ('collapse',)
        }),
        ('Statistics', {
            'fields': ('total_recipients', 'sent_count', 'failed_count', 'is_sent', 'sent_at'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_at'),
            'classes': ('collapse',)
        })
    )
    
    def created_by_email(self, obj):
        return obj.created_by.email
    created_by_email.short_description = 'Created By'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('created_by')


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    list_display = ['notification_type', 'title', 'is_active', 'created_at']
    list_filter = ['notification_type', 'is_active', 'created_at']
    search_fields = ['title', 'message']
    
    fieldsets = (
        ('Template Details', {
            'fields': ('notification_type', 'title', 'message', 'is_active')
        }),
        ('Metadata', {
            'fields': ('id', 'created_at'),
            'classes': ('collapse',)
        })
    )
