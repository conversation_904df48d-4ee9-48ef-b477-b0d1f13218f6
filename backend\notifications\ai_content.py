"""
AI-generated notification content pool
Predefined motivational and reminder messages for different notification types
"""

WORKOUT_REMINDERS = [
    {
        "title": "💪 Workout Time!",
        "message": "Hey {user_name}! Your workout session starts soon. Time to crush those fitness goals!"
    },
    {
        "title": "🔥 Get Ready to Sweat!",
        "message": "Hi {user_name}! Your workout is coming up. Let's make today count!"
    },
    {
        "title": "⚡ Power Up Time!",
        "message": "{user_name}, your training session is about to begin. You've got this!"
    },
    {
        "title": "🎯 Focus Mode Activated!",
        "message": "Ready {user_name}? Your workout starts soon. Time to show what you're made of!"
    },
    {
        "title": "🚀 Launch Into Action!",
        "message": "Hey {user_name}! Your workout is scheduled soon. Let's turn up the intensity!"
    },
    {
        "title": "💥 Beast Mode Loading...",
        "message": "{user_name}, time to unleash your inner athlete! Your workout awaits."
    },
    {
        "title": "🏆 Champion's Call!",
        "message": "Hi {user_name}! Champions train when others rest. Your workout time is here!"
    },
    {
        "title": "⭐ Shine Bright!",
        "message": "{user_name}, every rep counts! Your workout session is starting soon."
    }
]

MEAL_REMINDERS = [
    {
        "title": "🍽️ Fuel Time!",
        "message": "Hey {user_name}! Time to nourish your body with your planned meal."
    },
    {
        "title": "🥗 Nutrition Break!",
        "message": "Hi {user_name}! Your meal is ready. Let's fuel those gains!"
    },
    {
        "title": "🍎 Healthy Choice Time!",
        "message": "{user_name}, your body is calling for nutrition. Meal time!"
    },
    {
        "title": "⚡ Energy Refuel!",
        "message": "Ready {user_name}? Time to recharge with your healthy meal!"
    },
    {
        "title": "🌟 Nourish & Flourish!",
        "message": "Hey {user_name}! Great nutrition leads to great results. Meal time!"
    },
    {
        "title": "🥙 Tasty & Healthy!",
        "message": "{user_name}, your delicious and nutritious meal awaits!"
    },
    {
        "title": "🍓 Fresh & Fit!",
        "message": "Hi {user_name}! Time to treat your body right with your planned meal."
    },
    {
        "title": "🥑 Green & Clean!",
        "message": "{user_name}, healthy eating is self-care. Your meal is ready!"
    }
]

HYDRATION_REMINDERS = [
    {
        "title": "💧 Hydration Station!",
        "message": "Hey {user_name}! Time for a refreshing glass of water. Stay hydrated!"
    },
    {
        "title": "🌊 Water Break!",
        "message": "Hi {user_name}! Your body needs water. Take a hydration break!"
    },
    {
        "title": "💦 Refresh & Recharge!",
        "message": "{user_name}, keep those energy levels up with some H2O!"
    },
    {
        "title": "🚰 Thirst Quencher!",
        "message": "Ready {user_name}? Time to hydrate and feel great!"
    },
    {
        "title": "💧 Liquid Energy!",
        "message": "Hey {user_name}! Water is life. Time for your hydration boost!"
    },
    {
        "title": "🌊 Flow State!",
        "message": "{user_name}, keep the good vibes flowing with some water!"
    },
    {
        "title": "💦 Crystal Clear!",
        "message": "Hi {user_name}! Clear mind, clear body. Time to hydrate!"
    },
    {
        "title": "🥤 Sip & Shine!",
        "message": "{user_name}, every sip counts! Stay hydrated, stay awesome!"
    }
]

WEEKLY_MOTIVATION = [
    {
        "title": "🌟 Weekly Warrior!",
        "message": "Hey {user_name}! You're crushing it this week! Keep up the amazing work and stay consistent with your goals!"
    },
    {
        "title": "🚀 Momentum Master!",
        "message": "Hi {user_name}! Every small step leads to big changes. You're building incredible momentum!"
    },
    {
        "title": "💪 Strength Builder!",
        "message": "{user_name}, you're not just building muscle, you're building character! Keep pushing forward!"
    },
    {
        "title": "🎯 Goal Getter!",
        "message": "Ready {user_name}? Your dedication is inspiring! Every workout and healthy meal brings you closer to your dreams!"
    },
    {
        "title": "🏆 Champion Mindset!",
        "message": "Hey {user_name}! Champions aren't made overnight, but you're proving that consistency wins every time!"
    },
    {
        "title": "⭐ Transformation Tuesday!",
        "message": "{user_name}, transformation happens one day at a time. You're doing amazing!"
    },
    {
        "title": "🔥 Fire Within!",
        "message": "Hi {user_name}! The fire within you burns brighter than any obstacle. Keep that passion alive!"
    },
    {
        "title": "💎 Diamond in Progress!",
        "message": "{user_name}, pressure makes diamonds. Every challenge is shaping you into something incredible!"
    },
    {
        "title": "🌈 Progress Rainbow!",
        "message": "Hey {user_name}! After every storm comes a rainbow. Your hard work is creating something beautiful!"
    },
    {
        "title": "🎪 Circus Strong!",
        "message": "{user_name}, you're the star of your own fitness circus! Every rep is a performance worth celebrating!"
    }
]

def get_random_content(notification_type, user_name=""):
    """Get random notification content for a specific type"""
    import random
    
    content_map = {
        'WORKOUT_REMINDER': WORKOUT_REMINDERS,
        'MEAL_REMINDER': MEAL_REMINDERS,
        'HYDRATION_REMINDER': HYDRATION_REMINDERS,
        'WEEKLY_MOTIVATION': WEEKLY_MOTIVATION,
    }
    
    if notification_type not in content_map:
        return {
            "title": "AC-FIT Reminder",
            "message": f"Hey {user_name}! Don't forget to stay on track with your fitness goals!"
        }
    
    content = random.choice(content_map[notification_type])
    
    # Replace user_name placeholder
    return {
        "title": content["title"],
        "message": content["message"].format(user_name=user_name or "Champion")
    }
