<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AC-FIT Notification System Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
        }
        .code-block {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .example {
            background: #e8f5e8;
            border-left: 4px solid #27ae60;
            padding: 15px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .nav {
            background: #34495e;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .nav a {
            color: #3498db;
            text-decoration: none;
            margin-right: 20px;
        }
        .nav a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav">
            <strong>AC-FIT Admin Panel</strong> | 
            <a href="/acfit-admin/">Dashboard</a>
            <a href="/acfit-admin/notifications/adminnotificationcampaign/">Campaigns</a>
            <a href="/api/notifications/admin/identifiers-help/">API Help</a>
        </div>

        <h1>🔔 AC-FIT Notification System Documentation</h1>

        <h2>📋 Overview</h2>
        <p>The AC-FIT notification system is a comprehensive messaging platform that combines AI-generated reminders with admin-controlled campaigns. It uses local notifications (no Firebase required) to deliver personalized messages to users based on their workout plans, meal plans, and questionnaire responses.</p>

        <div class="info">
            <strong>Key Features:</strong> AI-generated reminders, admin campaigns, user targeting, local notifications, background sync, and complete user control over notification preferences.
        </div>

        <h2>🏗️ How The System Works</h2>

        <h3>📱 Local Notification Architecture</h3>
        <p>The AC-FIT notification system uses <strong>local notifications only</strong> - no Firebase or external push notification services required. Here's how it works:</p>

        <div class="code-block">
<strong>1. Server Generation:</strong> Backend creates notifications based on user schedules and admin campaigns
<strong>2. Background Sync:</strong> Flutter app syncs with server every hour to fetch new notifications
<strong>3. Local Display:</strong> Notifications are displayed immediately using device's local notification system
<strong>4. User Control:</strong> Users can manage all notification types through in-app settings
        </div>

        <h3>🔄 Notification Flow</h3>
        <ol>
            <li><strong>AI Content Pool:</strong> System has 34 pre-written AI messages for different notification types</li>
            <li><strong>Schedule Detection:</strong> Backend monitors user workout/meal schedules and generates reminders</li>
            <li><strong>Admin Campaigns:</strong> Admins create targeted campaigns with personalized messages</li>
            <li><strong>Database Storage:</strong> All notifications stored in database with read/unread status</li>
            <li><strong>Background Sync:</strong> Flutter app pulls new notifications every hour</li>
            <li><strong>Local Delivery:</strong> Notifications displayed using device's notification system</li>
            <li><strong>In-App History:</strong> Users can view notification history and manage settings</li>
        </ol>

        <h3>⚙️ Technical Components</h3>
        <div class="info">
            <strong>Backend Services:</strong>
            <ul>
                <li><strong>NotificationService:</strong> Core notification creation and management</li>
                <li><strong>ReminderService:</strong> Automated reminder generation based on schedules</li>
                <li><strong>AdminNotificationService:</strong> Campaign creation and user targeting</li>
            </ul>
        </div>

        <div class="warning">
            <strong>Frontend Services:</strong>
            <ul>
                <li><strong>LocalNotificationService:</strong> Device notification display and channels</li>
                <li><strong>BackgroundNotificationService:</strong> Hourly sync and reminder scheduling</li>
                <li><strong>NotificationApiService:</strong> Communication with backend APIs</li>
            </ul>
        </div>

        <h2>🎯 Notification Types</h2>

        <h3>🤖 Automated Reminders (AI-Generated)</h3>
        <table>
            <tr>
                <th>Type</th>
                <th>Description</th>
                <th>Frequency</th>
                <th>User Control</th>
            </tr>
            <tr>
                <td>Workout Reminders</td>
                <td>Sent before scheduled workouts</td>
                <td>30 minutes before (configurable)</td>
                <td>Can be disabled</td>
            </tr>
            <tr>
                <td>Meal Reminders</td>
                <td>Sent before meal times</td>
                <td>15 minutes before (configurable)</td>
                <td>Can be disabled</td>
            </tr>
            <tr>
                <td>Hydration Reminders</td>
                <td>Regular water intake reminders</td>
                <td>Every 2 hours (configurable)</td>
                <td>Can be disabled</td>
            </tr>
            <tr>
                <td>Weekly Motivation</td>
                <td>Motivational messages</td>
                <td>Once per week (configurable day/time)</td>
                <td>Can be disabled</td>
            </tr>
        </table>

        <h3>Admin Messages</h3>
        <p>Administrators can send targeted notifications to users based on various criteria.</p>

        <h2>🎯 Admin Panel Functionality</h2>

        <h3>📊 Accessing Notification Management</h3>
        <p>The notification system is fully integrated into the AC-FIT admin panel:</p>

        <div class="code-block">
<strong>Admin Header:</strong> Click the purple "Notifications" button in the admin header
<strong>Direct URL:</strong> /acfit-admin/notifications/adminnotificationcampaign/
<strong>Dashboard:</strong> Notifications tile on the main admin dashboard
        </div>

        <h3>🏗️ Admin Panel Features</h3>
        <table>
            <tr>
                <th>Feature</th>
                <th>Description</th>
                <th>Location</th>
            </tr>
            <tr>
                <td><strong>Campaign Management</strong></td>
                <td>Create, edit, and send notification campaigns</td>
                <td>Notifications → Admin notification campaigns</td>
            </tr>
            <tr>
                <td><strong>User Settings</strong></td>
                <td>View and modify user notification preferences</td>
                <td>Notifications → User notification settings</td>
            </tr>
            <tr>
                <td><strong>Notification History</strong></td>
                <td>View all sent notifications and their status</td>
                <td>Notifications → Notification logs</td>
            </tr>
            <tr>
                <td><strong>AI Templates</strong></td>
                <td>Manage the AI-generated message templates</td>
                <td>Notifications → Notification templates</td>
            </tr>
        </table>

        <h3>🎨 Admin Interface Design</h3>
        <p>The admin interface follows AC-FIT's purple theme with consistent styling:</p>
        <ul>
            <li><strong>Purple Buttons:</strong> All navigation buttons use #6b46c1 background</li>
            <li><strong>Hover Effects:</strong> Darker purple (#553c9a) on hover</li>
            <li><strong>Consistent Layout:</strong> Matches existing admin panel design</li>
            <li><strong>Responsive Design:</strong> Works on desktop and tablet devices</li>
        </ul>

        <h2>🎯 Targeting Options</h2>

        <h3>All Users</h3>
        <p>Send notifications to all active users in the system.</p>

        <h3>Workout Plan Targeting</h3>
        <p>Target users who are currently assigned to specific workout plans.</p>
        <div class="example">
            <strong>Example:</strong> Send a motivation message to all users on the "Beginner Strength Training" plan.
        </div>

        <h3>Meal Plan Targeting</h3>
        <p>Target users who are currently assigned to specific meal plans.</p>
        <div class="example">
            <strong>Example:</strong> Send nutrition tips to users on the "Keto Diet Plan".
        </div>

        <h3>Questionnaire-Based Targeting</h3>
        <p>Target users based on their questionnaire responses.</p>
        <div class="example">
            <strong>Example:</strong> Send beginner tips to users with fitness_level = "BEGINNER".
        </div>

        <h2>🏷️ Message Identifiers</h2>
        <p>Use these identifiers in your messages to personalize content for each user:</p>

        <h3>User Information</h3>
        <table>
            <tr>
                <th>Identifier</th>
                <th>Description</th>
                <th>Example Output</th>
            </tr>
            <tr>
                <td><code>{user.name}</code></td>
                <td>User's first name or email username</td>
                <td>John or john.doe</td>
            </tr>
            <tr>
                <td><code>{user.first_name}</code></td>
                <td>User's first name</td>
                <td>John</td>
            </tr>
            <tr>
                <td><code>{user.last_name}</code></td>
                <td>User's last name</td>
                <td>Doe</td>
            </tr>
            <tr>
                <td><code>{user.email}</code></td>
                <td>User's email address</td>
                <td><EMAIL></td>
            </tr>
        </table>

        <h3>Plan Information</h3>
        <table>
            <tr>
                <th>Identifier</th>
                <th>Description</th>
                <th>Example Output</th>
            </tr>
            <tr>
                <td><code>{user.workout_plan}</code></td>
                <td>Current active workout plan</td>
                <td>Beginner Strength Training</td>
            </tr>
            <tr>
                <td><code>{user.meal_plan}</code></td>
                <td>Current active meal plan</td>
                <td>Balanced Nutrition Plan</td>
            </tr>
        </table>

        <h3>Profile Information</h3>
        <table>
            <tr>
                <th>Identifier</th>
                <th>Description</th>
                <th>Example Output</th>
            </tr>
            <tr>
                <td><code>{user.fitness_goal}</code></td>
                <td>User's primary fitness goal</td>
                <td>Lose Weight</td>
            </tr>
            <tr>
                <td><code>{user.fitness_level}</code></td>
                <td>User's fitness level</td>
                <td>Beginner</td>
            </tr>
            <tr>
                <td><code>{user.age_group}</code></td>
                <td>User's age group</td>
                <td>30-40</td>
            </tr>
        </table>

        <h2>💬 Message Examples</h2>

        <div class="example">
            <h4>Personalized Workout Motivation</h4>
            <div class="code-block">
Hey {user.name}! Ready to crush your {user.workout_plan} session today? 
Your {user.fitness_goal} journey is going amazing! 💪
            </div>
        </div>

        <div class="example">
            <h4>Meal Plan Reminder</h4>
            <div class="code-block">
Hi {user.first_name}! Don't forget about your {user.meal_plan} meal coming up. 
Staying consistent with nutrition is key to reaching your goals! 🍽️
            </div>
        </div>

        <div class="example">
            <h4>Progress Encouragement</h4>
            <div class="code-block">
{user.name}, you're doing fantastic on your {user.fitness_goal} journey! 
Keep up the great work with your {user.workout_plan}. Every workout counts! 🌟
            </div>
        </div>

        <h2>📊 Campaign Management</h2>

        <h3>Creating Campaigns</h3>
        <ol>
            <li>Navigate to the <strong>Admin Notification Campaigns</strong> section</li>
            <li>Click <strong>"Add Admin notification campaign"</strong></li>
            <li>Fill in the campaign details:
                <ul>
                    <li><strong>Title:</strong> Brief description of the campaign</li>
                    <li><strong>Message:</strong> The notification content (use identifiers for personalization)</li>
                    <li><strong>Target Type:</strong> Choose your targeting method</li>
                    <li><strong>Targeting Criteria:</strong> Set specific criteria based on target type</li>
                </ul>
            </li>
            <li>Save the campaign</li>
            <li>Use the <strong>"Send"</strong> action to deliver the notifications</li>
        </ol>

        <h3>Campaign Statistics</h3>
        <p>After sending a campaign, you can view:</p>
        <ul>
            <li><strong>Total Recipients:</strong> Number of users targeted</li>
            <li><strong>Sent Count:</strong> Successfully delivered notifications</li>
            <li><strong>Failed Count:</strong> Failed delivery attempts</li>
            <li><strong>Send Time:</strong> When the campaign was executed</li>
        </ul>

        <h2>⚙️ User Settings</h2>
        <p>Users can control their notification preferences through the app settings:</p>
        <ul>
            <li>Enable/disable each notification type</li>
            <li>Adjust timing for reminders</li>
            <li>Set hydration reminder intervals</li>
            <li>Choose weekly motivation day and time</li>
        </ul>

        <div class="info">
            <strong>Note:</strong> Admin messages have a separate toggle, so users can disable promotional content while keeping important reminders.
        </div>

        <h2>🔧 API Endpoints</h2>
        <p>For programmatic access, the following API endpoints are available:</p>

        <div class="code-block">
POST /api/notifications/admin/send/
GET /api/notifications/admin/identifiers-help/
GET /api/notifications/admin/campaigns/targeting_options/
        </div>

        <div class="warning">
            <strong>Important:</strong> All admin API endpoints require authentication and admin privileges.
        </div>

        <h2>📱 Local Notification System</h2>

        <h3>🔔 How Local Notifications Work</h3>
        <p>AC-FIT uses <strong>local notifications only</strong> - no Firebase or external push services required. This provides better privacy and reliability:</p>

        <div class="info">
            <strong>Local Notifications Benefits:</strong>
            <ul>
                <li><strong>Privacy:</strong> No data sent to external services</li>
                <li><strong>Reliability:</strong> Works offline and doesn't depend on external servers</li>
                <li><strong>Performance:</strong> Faster delivery and lower battery usage</li>
                <li><strong>Control:</strong> Users have complete control over notification settings</li>
            </ul>
        </div>

        <h3>📲 User Experience Flow</h3>
        <ol>
            <li><strong>Background Sync:</strong> App syncs with server every hour to fetch new notifications</li>
            <li><strong>Local Storage:</strong> Notifications stored locally on device</li>
            <li><strong>Immediate Display:</strong> New notifications shown instantly using device's notification system</li>
            <li><strong>Notification Channels:</strong> Different types use separate channels with appropriate priority</li>
            <li><strong>In-App Management:</strong> Users can view history and manage settings in the app</li>
        </ol>

        <h3>🎯 Notification Channels</h3>
        <table>
            <tr>
                <th>Channel</th>
                <th>Priority</th>
                <th>Description</th>
                <th>User Control</th>
            </tr>
            <tr>
                <td><strong>Workout Reminders</strong></td>
                <td>High</td>
                <td>30 minutes before scheduled workouts</td>
                <td>Can disable, adjust timing (5-120 min)</td>
            </tr>
            <tr>
                <td><strong>Meal Reminders</strong></td>
                <td>High</td>
                <td>15 minutes before meal times</td>
                <td>Can disable, adjust timing (5-60 min)</td>
            </tr>
            <tr>
                <td><strong>Hydration Reminders</strong></td>
                <td>Default</td>
                <td>Every 2 hours during active hours</td>
                <td>Can disable, adjust interval (1-12 hours)</td>
            </tr>
            <tr>
                <td><strong>Weekly Motivation</strong></td>
                <td>Default</td>
                <td>Once per week at chosen time</td>
                <td>Can disable, choose day and time</td>
            </tr>
            <tr>
                <td><strong>Admin Messages</strong></td>
                <td>High</td>
                <td>Important announcements and campaigns</td>
                <td>Can disable separately from reminders</td>
            </tr>
        </table>

        <h3>⚙️ User Notification Settings</h3>
        <p>Users have complete control over their notification experience through the app settings:</p>

        <div class="code-block">
<strong>Individual Toggles:</strong> Enable/disable each notification type separately
<strong>Timing Control:</strong> Adjust reminder timing for workouts and meals
<strong>Hydration Intervals:</strong> Set custom intervals between hydration reminders
<strong>Weekly Schedule:</strong> Choose day and time for weekly motivation messages
<strong>Admin Messages:</strong> Separate toggle for admin campaigns and announcements
        </div>

        <h3>📊 Notification History & Management</h3>
        <p>The app provides comprehensive notification management:</p>
        <ul>
            <li><strong>Notification Icon:</strong> Home screen icon with unread badge count</li>
            <li><strong>History Screen:</strong> View all past notifications with timestamps</li>
            <li><strong>Read/Unread Status:</strong> Track which notifications have been viewed</li>
            <li><strong>Mark as Read:</strong> Individual and bulk read actions</li>
            <li><strong>Settings Sync:</strong> Preferences synchronized with server</li>
        </ul>

        <h2>📱 Mobile App Integration</h2>

        <h3>🔧 Technical Implementation</h3>
        <p>The notification system integrates seamlessly with the Flutter app:</p>

        <div class="warning">
            <strong>Key Components:</strong>
            <ul>
                <li><strong>Background Service:</strong> Runs every hour to sync notifications</li>
                <li><strong>Local Database:</strong> Stores notifications and settings locally</li>
                <li><strong>Notification Channels:</strong> Separate channels for different types</li>
                <li><strong>API Integration:</strong> RESTful communication with backend</li>
            </ul>
        </div>

        <h2>🤖 AI Content Pool System</h2>

        <h3>📝 How AI Messages Work</h3>
        <p>AC-FIT includes a comprehensive AI-generated content pool with 34 unique messages across all notification types. This ensures users receive varied, engaging content instead of repetitive reminders.</p>

        <div class="info">
            <strong>AI Content Statistics:</strong>
            <ul>
                <li><strong>Workout Reminders:</strong> 8 unique motivational messages</li>
                <li><strong>Meal Reminders:</strong> 8 unique food-focused messages</li>
                <li><strong>Hydration Reminders:</strong> 8 unique water intake messages</li>
                <li><strong>Weekly Motivation:</strong> 10 unique inspirational messages</li>
            </ul>
        </div>

        <h3>🎯 Message Selection Algorithm</h3>
        <p>The system intelligently selects messages to provide variety:</p>
        <ol>
            <li><strong>Random Selection:</strong> Messages chosen randomly from the appropriate pool</li>
            <li><strong>Type Matching:</strong> Workout reminders use workout-specific messages</li>
            <li><strong>Context Awareness:</strong> Messages consider user's current plan and progress</li>
            <li><strong>Variety Assurance:</strong> System avoids repeating the same message too frequently</li>
        </ol>

        <h3>📋 Sample AI Messages</h3>
        <table>
            <tr>
                <th>Type</th>
                <th>Sample Messages</th>
            </tr>
            <tr>
                <td><strong>Workout</strong></td>
                <td>
                    "Time to crush your workout! 💪 Your future self will thank you."<br>
                    "Ready to get stronger? Your workout is starting soon!"<br>
                    "Let's turn those goals into gains! Workout time approaches."
                </td>
            </tr>
            <tr>
                <td><strong>Meal</strong></td>
                <td>
                    "Fuel your body right! 🍎 Your meal is ready to be enjoyed."<br>
                    "Time to nourish yourself! Your planned meal awaits."<br>
                    "Good nutrition = better results! Meal time is here."
                </td>
            </tr>
            <tr>
                <td><strong>Hydration</strong></td>
                <td>
                    "Stay hydrated, stay healthy! 💧 Time for some water."<br>
                    "Your body needs water to perform its best! Drink up!"<br>
                    "Hydration check! Keep that water intake consistent."
                </td>
            </tr>
            <tr>
                <td><strong>Motivation</strong></td>
                <td>
                    "You're making amazing progress! Keep up the fantastic work! 🌟"<br>
                    "Every step forward is a victory! You're doing great!"<br>
                    "Consistency is key, and you're nailing it! Stay strong!"
                </td>
            </tr>
        </table>

        <h3>⚙️ Managing AI Templates</h3>
        <p>Administrators can manage the AI content pool through the admin panel:</p>
        <div class="code-block">
<strong>View Templates:</strong> Notifications → Notification templates
<strong>Edit Messages:</strong> Modify existing AI-generated content
<strong>Add New:</strong> Create additional message variations
<strong>Categorize:</strong> Assign messages to specific notification types
        </div>

        <h2>🛠️ Best Practices</h2>
        <ul>
            <li><strong>Personalization:</strong> Always use identifiers to make messages personal</li>
            <li><strong>Timing:</strong> Consider user time zones when sending notifications</li>
            <li><strong>Frequency:</strong> Avoid overwhelming users with too many messages</li>
            <li><strong>Value:</strong> Ensure each notification provides value to the user</li>
            <li><strong>Testing:</strong> Test campaigns with a small group before broad deployment</li>
        </ul>

        <div class="info">
            <strong>Support:</strong> For technical issues or questions about the notification system, contact the development team.
        </div>
    </div>
</body>
</html>
