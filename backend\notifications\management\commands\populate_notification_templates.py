"""
Management command to populate notification templates with AI-generated content
"""
from django.core.management.base import BaseCommand
from notifications.models import NotificationTemplate
from notifications.ai_content import (
    WORKOUT_REMINDERS, MEAL_REMINDERS, 
    HYDRATION_REMINDERS, WEEKLY_MOTIVATION
)


class Command(BaseCommand):
    help = 'Populate notification templates with AI-generated content'
    
    def handle(self, *args, **options):
        self.stdout.write('Populating notification templates...')
        
        # Clear existing templates
        NotificationTemplate.objects.all().delete()
        
        templates_created = 0
        
        # Create workout reminder templates
        for content in WORKOUT_REMINDERS:
            NotificationTemplate.objects.create(
                notification_type='WORKOUT_REMINDER',
                title=content['title'],
                message=content['message']
            )
            templates_created += 1
        
        # Create meal reminder templates
        for content in MEAL_REMINDERS:
            NotificationTemplate.objects.create(
                notification_type='MEAL_REMINDER',
                title=content['title'],
                message=content['message']
            )
            templates_created += 1
        
        # Create hydration reminder templates
        for content in HYDRATION_REMINDERS:
            NotificationTemplate.objects.create(
                notification_type='HYDRATION_REMINDER',
                title=content['title'],
                message=content['message']
            )
            templates_created += 1
        
        # Create weekly motivation templates
        for content in WEEKLY_MOTIVATION:
            NotificationTemplate.objects.create(
                notification_type='WEEKLY_MOTIVATION',
                title=content['title'],
                message=content['message']
            )
            templates_created += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {templates_created} notification templates'
            )
        )
