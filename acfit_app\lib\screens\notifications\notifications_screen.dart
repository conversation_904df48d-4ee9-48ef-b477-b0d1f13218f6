import 'package:flutter/material.dart';
import '../../models/notification_log.dart';
import '../../services/notification_api_service.dart';
import '../../utils/logger.dart';
import '../../widgets/common/animated_loading.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  List<NotificationLog> _notifications = [];
  bool _isLoading = true;
  bool _isMarkingAsRead = false;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    try {
      setState(() => _isLoading = true);

      final notifications = await NotificationApiService.getNotificationLogs();

      setState(() {
        _notifications = notifications;
        _isLoading = false;
      });
    } catch (e) {
      Logger.log('Error loading notifications: $e', tag: 'NotificationsScreen');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _markAllAsRead() async {
    final unreadNotifications =
        _notifications.where((n) => n.isUnread).toList();

    if (unreadNotifications.isEmpty) return;

    try {
      setState(() => _isMarkingAsRead = true);

      final notificationIds = unreadNotifications.map((n) => n.id).toList();
      final success = await NotificationApiService.markAsRead(notificationIds);

      if (success) {
        await _loadNotifications(); // Reload to get updated status
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('All notifications marked as read')),
        );
      }
    } catch (e) {
      Logger.log('Error marking notifications as read: $e',
          tag: 'NotificationsScreen');
    } finally {
      setState(() => _isMarkingAsRead = false);
    }
  }

  Future<void> _markAsRead(NotificationLog notification) async {
    if (notification.isRead) return;

    try {
      final success =
          await NotificationApiService.markAsRead([notification.id]);

      if (success) {
        setState(() {
          final index =
              _notifications.indexWhere((n) => n.id == notification.id);
          if (index != -1) {
            _notifications[index] = NotificationLog(
              id: notification.id,
              notificationType: notification.notificationType,
              title: notification.title,
              message: notification.message,
              status: 'READ',
              sentAt: notification.sentAt,
              readAt: DateTime.now(),
              createdAt: notification.createdAt,
              metadata: notification.metadata,
            );
          }
        });
      }
    } catch (e) {
      Logger.log('Error marking notification as read: $e',
          tag: 'NotificationsScreen');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        actions: [
          if (_notifications.any((n) => n.isUnread))
            TextButton(
              onPressed: _isMarkingAsRead ? null : _markAllAsRead,
              child: _isMarkingAsRead
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Mark All Read'),
            ),
        ],
      ),
      body: _isLoading
          ? const AnimatedLoading()
          : _notifications.isEmpty
              ? _buildEmptyState()
              : RefreshIndicator(
                  onRefresh: _loadNotifications,
                  child: ListView.builder(
                    itemCount: _notifications.length,
                    itemBuilder: (context, index) {
                      final notification = _notifications[index];
                      return _buildNotificationTile(notification);
                    },
                  ),
                ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'No notifications yet',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'You\'ll see your reminders and messages here',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationTile(NotificationLog notification) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: notification.isUnread
              ? Theme.of(context).primaryColor
              : Colors.grey[300],
          child: Text(
            notification.icon,
            style: const TextStyle(fontSize: 20),
          ),
        ),
        title: Text(
          notification.title,
          style: TextStyle(
            fontWeight:
                notification.isUnread ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              notification.message,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  notification.displayType,
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Text(
                  _formatTime(notification.createdAt),
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () => _markAsRead(notification),
        trailing: notification.isUnread
            ? Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  shape: BoxShape.circle,
                ),
              )
            : null,
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
