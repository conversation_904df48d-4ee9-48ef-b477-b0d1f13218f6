class NotificationSettings {
  final bool workoutRemindersEnabled;
  final bool mealRemindersEnabled;
  final bool hydrationRemindersEnabled;
  final bool weeklyMotivationEnabled;
  final bool adminMessagesEnabled;
  final int hydrationIntervalHours;
  final int workoutReminderMinutes;
  final int mealReminderMinutes;
  final int weeklyMotivationDay;
  final int weeklyMotivationHour;

  NotificationSettings({
    required this.workoutRemindersEnabled,
    required this.mealRemindersEnabled,
    required this.hydrationRemindersEnabled,
    required this.weeklyMotivationEnabled,
    required this.adminMessagesEnabled,
    required this.hydrationIntervalHours,
    required this.workoutReminderMinutes,
    required this.mealReminderMinutes,
    required this.weeklyMotivationDay,
    required this.weeklyMotivationHour,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      workoutRemindersEnabled: json['workout_reminders_enabled'] as bool? ?? true,
      mealRemindersEnabled: json['meal_reminders_enabled'] as bool? ?? true,
      hydrationRemindersEnabled: json['hydration_reminders_enabled'] as bool? ?? true,
      weeklyMotivationEnabled: json['weekly_motivation_enabled'] as bool? ?? true,
      adminMessagesEnabled: json['admin_messages_enabled'] as bool? ?? true,
      hydrationIntervalHours: json['hydration_interval_hours'] as int? ?? 2,
      workoutReminderMinutes: json['workout_reminder_minutes'] as int? ?? 30,
      mealReminderMinutes: json['meal_reminder_minutes'] as int? ?? 15,
      weeklyMotivationDay: json['weekly_motivation_day'] as int? ?? 1,
      weeklyMotivationHour: json['weekly_motivation_hour'] as int? ?? 9,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'workout_reminders_enabled': workoutRemindersEnabled,
      'meal_reminders_enabled': mealRemindersEnabled,
      'hydration_reminders_enabled': hydrationRemindersEnabled,
      'weekly_motivation_enabled': weeklyMotivationEnabled,
      'admin_messages_enabled': adminMessagesEnabled,
      'hydration_interval_hours': hydrationIntervalHours,
      'workout_reminder_minutes': workoutReminderMinutes,
      'meal_reminder_minutes': mealReminderMinutes,
      'weekly_motivation_day': weeklyMotivationDay,
      'weekly_motivation_hour': weeklyMotivationHour,
    };
  }

  NotificationSettings copyWith({
    bool? workoutRemindersEnabled,
    bool? mealRemindersEnabled,
    bool? hydrationRemindersEnabled,
    bool? weeklyMotivationEnabled,
    bool? adminMessagesEnabled,
    int? hydrationIntervalHours,
    int? workoutReminderMinutes,
    int? mealReminderMinutes,
    int? weeklyMotivationDay,
    int? weeklyMotivationHour,
  }) {
    return NotificationSettings(
      workoutRemindersEnabled: workoutRemindersEnabled ?? this.workoutRemindersEnabled,
      mealRemindersEnabled: mealRemindersEnabled ?? this.mealRemindersEnabled,
      hydrationRemindersEnabled: hydrationRemindersEnabled ?? this.hydrationRemindersEnabled,
      weeklyMotivationEnabled: weeklyMotivationEnabled ?? this.weeklyMotivationEnabled,
      adminMessagesEnabled: adminMessagesEnabled ?? this.adminMessagesEnabled,
      hydrationIntervalHours: hydrationIntervalHours ?? this.hydrationIntervalHours,
      workoutReminderMinutes: workoutReminderMinutes ?? this.workoutReminderMinutes,
      mealReminderMinutes: mealReminderMinutes ?? this.mealReminderMinutes,
      weeklyMotivationDay: weeklyMotivationDay ?? this.weeklyMotivationDay,
      weeklyMotivationHour: weeklyMotivationHour ?? this.weeklyMotivationHour,
    );
  }

  String get weeklyMotivationDayName {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return days[weeklyMotivationDay];
  }

  String get weeklyMotivationTimeString {
    final hour = weeklyMotivationHour;
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '$displayHour:00 $period';
  }
}
