import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:dio/dio.dart'; // Import DioException
import 'dart:developer' as developer; // Import developer
import 'package:provider/provider.dart'; // Import Provider

import '../models/user.dart';
import '../models/questionnaire_data.dart'; // Import QuestionnaireData
import 'api_service.dart';
import '../constants/api_constants.dart'; // Import ApiConstants
import '../providers/user_progress_provider.dart'; // Import UserProgressProvider
import 'navigation_service.dart'; // Import NavigationService

class AuthService with ChangeNotifier {
  final ApiService _apiService = ApiService();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  // Keys for secure storage
  static const String _accessTokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';

  User? _currentUser;
  bool _isLoading = false;
  String? _error;
  bool _isAuthenticated = false;
  bool _isCheckingAuth = false;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _isAuthenticated;

  AuthService() {
    // Constructor is now empty - checkAuthStatus will be called explicitly elsewhere (e.g., SplashScreen)
  }

  // Check authentication status explicitly when needed
  Future<void> checkAuthStatus() async {
    if (_isCheckingAuth) {
      return;
    }

    try {
      _isCheckingAuth = true;

      final token = await _secureStorage.read(key: _accessTokenKey);
      if (token == null || token.isEmpty) {
        _isAuthenticated = false;
        return;
      }

      _isAuthenticated = await _validateToken();

      if (_isAuthenticated) {
        // Fetch user profile
        await getUserProfile();
      }
    } catch (e) {
      _error = 'Failed to check authentication status';
      _isAuthenticated = false;
    } finally {
      _isCheckingAuth = false;
    }
  }

  // Get user profile
  Future<User?> getUserProfile() async {
    try {
      // Fetch full user details to populate _currentUser correctly.
      final userData = await _apiService.get(ApiConstants.userDetails);
      if (userData != null && userData is Map<String, dynamic>) {
        _currentUser = User.fromJson(userData);
        developer.log(
            'User profile fetched. Parsed User: ID=${_currentUser?.id}, Email=${_currentUser?.email}, Name=${_currentUser?.firstName}',
            name: 'AuthService.getUserProfile');
        notifyListeners(); // Notify listeners after updating user data
      } else {
        developer.log('Failed to parse user details from /users/me/.',
            name: 'AuthService.getUserProfile', level: 900);
      }
      return _currentUser;
    } catch (e) {
      _error = 'Failed to load user profile: ${e.toString()}';
      developer.log('Error fetching user profile: $_error',
          name: 'AuthService.getUserProfile', error: e, level: 1000);
      notifyListeners();
      return null;
    }
  }

  // Get access token
  Future<String?> getToken() async {
    return await _apiService.getAccessToken();
  }

  // Get token with prefix for HTTP header
  Future<String> getAuthHeader() async {
    final token = await _secureStorage.read(key: _accessTokenKey);
    if (token == null) {
      return '';
    }
    return token;
  }

  // Login method
  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final result = await _apiService.login(email, password);

      // Check for email verification error
      if (result['error'] == 'Email not verified') {
        return {
          'success': false,
          'requires_verification': true,
          'email': email
        };
      }

      if (result['status'] == 'success') {
        _isAuthenticated = true;

        final data = result['data'];
        if (data != null) {
          // Store tokens if available
          if (data['access'] != null) {
            await _secureStorage.write(
                key: _accessTokenKey, value: data['access']);
          }
          if (data['refresh'] != null) {
            await _secureStorage.write(
                key: _refreshTokenKey, value: data['refresh']);
          }

          // Set user data if available
          if (data['user'] != null) {
            final userData = data['user'];
            if (userData is Map<String, dynamic>) {
              _currentUser = User.fromJson(userData);
            }
          }

          // Clear all caches to ensure fresh data
          _apiService.clearAllCaches();
        }

        notifyListeners();
        return {'success': true};
      } else {
        _error = result['error']?['message'] ?? 'Login failed';
        _isAuthenticated = false;
        notifyListeners();
        return {'success': false, 'error': _error};
      }
    } catch (e) {
      _error = 'Login error: ${e.toString()}';
      _isAuthenticated = false;
      notifyListeners();
      return {'success': false, 'error': _error};
    }
  }

  // Register method
  Future<Map<String, dynamic>> register(Map<String, dynamic> userData) async {
    try {
      _isLoading = true;
      _error = null;
      _isAuthenticated = false;

      // Step 1: Register the user
      final apiResult = await _apiService.register(userData);

      // Check if email verification is required (status from ApiService)
      if (apiResult['status'] == 'requires_verification') {
        _isLoading = false;
        notifyListeners();
        return {
          'success':
              true, // Indicate that the registration process itself was successful
          'requires_verification': true,
          'email': userData['email'],
          'verification_code': apiResult['data']
              ?['verification_code'], // Pass code for debug/testing
        };
      } else if (apiResult['status'] == 'success') {
        // This means registration was successful AND no verification was required, or login after registration succeeded

        if (userData.containsKey('email') && userData.containsKey('password')) {
          // Step 2: Login to get the authentication token
          final loginResult =
              await login(userData['email'], userData['password']);

          if (loginResult['success']) {
            _isLoading = false;
            notifyListeners();
            return {'success': true};
          } else {
            _error = 'Registration successful but could not log in: $_error';
            _isLoading = false;
            _isAuthenticated = false;
            notifyListeners();
            return {'success': false, 'error': _error};
          }
        } else {
          _error =
              'Registration successful but login credentials are incomplete';
          _isLoading = false;
          _isAuthenticated = false;
          notifyListeners();
          return {'success': false, 'error': _error};
        }
      } else {
        _error = apiResult['error']?['message'] ?? 'Registration failed';
        _isLoading = false;
        _isAuthenticated = false;
        notifyListeners();
        return {'success': false, 'error': _error};
      }
    } catch (e) {
      _error = 'Registration error: $e';
      _isLoading = false;
      _isAuthenticated = false;
      notifyListeners();
      return {'success': false, 'error': _error};
    }
  }

  // Logout method
  Future<bool> logout() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Call the API logout method (which returns void)
      await _apiService.logout();
      // No need to check the result as it's void. Assume success if no exception.

      // Clear tokens regardless of server response
      await _secureStorage.delete(key: _accessTokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);

      // Clear all caches to ensure fresh data on next login
      _apiService.clearAllCaches();

      // Always clear local state and return true after attempting logout and clearing tokens
      _currentUser = null;
      _isAuthenticated = false;
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      // Still clear tokens and user data on error
      await _secureStorage.delete(key: _accessTokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);

      // Clear all caches to ensure fresh data on next login
      _apiService.clearAllCaches();

      _currentUser = null;
      _isAuthenticated = false;

      _error = 'Logout completed with error: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return true; // Still return true as the client-side logout succeeded
    }
  }

  // Resend verification email - now sends a verification code
  Future<bool> resendVerificationEmail(String email) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _apiService.post(
        '/api/accounts/auth/send-verification-code/',
        data: {'email': email, 'code_type': 'email'},
      );

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Failed to resend verification email: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Verify email with code
  Future<bool> verifyEmailWithCode(String email, String code) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await _apiService.post(
        '/api/accounts/auth/verify-email-code/',
        data: {'email': email, 'code': code},
      );

      // Store tokens if available
      if (response != null && response is Map<String, dynamic>) {
        if (response['refresh'] != null) {
          await _secureStorage.write(
              key: _refreshTokenKey, value: response['refresh']);
        }
        if (response['access'] != null) {
          await _secureStorage.write(
              key: _accessTokenKey, value: response['access']);
        }

        // Set user data if available
        if (response['user'] != null) {
          final userData = response['user'];
          if (userData is Map<String, dynamic>) {
            _currentUser = User.fromJson(userData);
          }
        }

        // Set authenticated state
        _isAuthenticated = true;

        // Clear all caches to ensure fresh data
        _apiService.clearAllCaches();

        // Add a small delay to ensure UI updates are complete
        await Future.delayed(const Duration(milliseconds: 500));

        // Navigate to questionnaire after successful verification
        NavigationService.navigateToReplacementNamed(
            NavigationService.questionnaire);
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Failed to verify email: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Reset password - now sends a verification code
  Future<bool> resetPassword(String email) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _apiService.post(
        '/api/accounts/auth/send-verification-code/',
        data: {'email': email, 'code_type': 'password'},
      );

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Password reset failed: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Verify password reset code
  Future<bool> verifyPasswordResetCode(String email, String code) async {
    try {
      await _apiService.post(
        '/api/accounts/auth/password/reset/verify-code/',
        data: {
          'email': email,
          'code': code,
        },
      );

      return true;
    } catch (e) {
      _error = 'Failed to verify reset code: ${e.toString()}';
      return false;
    }
  }

  // Get latest verification code for development purposes
  Future<String?> getLatestVerificationCode(
      String email, String codeType) async {
    try {
      final response = await _apiService.get(
        '/api/accounts/auth/get-latest-verification-code/',
        queryParameters: {
          'email': email,
          'code_type': codeType,
        },
      );

      if (response != null && response is Map<String, dynamic>) {
        return response['code'] as String?;
      }
      return null;
    } catch (e) {
      developer.log('Failed to get latest verification code: $e',
          name: 'AuthService.getLatestVerificationCode');
      return null;
    }
  }

  // Reset password with code
  Future<bool> resetPasswordWithCode(
      String email, String code, String newPassword) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _apiService.post(
        '/api/accounts/auth/password/reset/confirm-code/',
        data: {
          'email': email,
          'code': code,
          'new_password1': newPassword,
          'new_password2': newPassword,
        },
      );

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Password reset failed: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Confirm password reset (for link-based reset)
  Future<bool> confirmPasswordReset(
      String uid, String token, String newPassword) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _apiService.post(
        '/api/accounts/auth/password/reset/confirm/',
        data: {
          'uid': uid,
          'token': token,
          'new_password1': newPassword,
          'new_password2': newPassword,
        },
      );

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Password reset confirmation failed: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Change password
  Future<bool> changePassword(String oldPassword, String newPassword) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final result = await _apiService.changePassword(oldPassword, newPassword);

      if (result['success']) {
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = result['message'] ?? 'Password change failed';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'Password change failed: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Check if token is valid by attempting to fetch user profile
  Future<bool> _validateToken() async {
    developer.log('Validating token by fetching user profile...',
        name: 'AuthService._validateToken');
    try {
      final token = await _secureStorage.read(key: _accessTokenKey);
      if (token == null || token.isEmpty) {
        developer.log('No token found for validation.',
            name: 'AuthService._validateToken');
        return false;
      }

      // Log the token format for debugging
      developer.log(
          'Token format for validation: ${token.length > 10 ? "${token.substring(0, 10)}..." : token}',
          name: 'AuthService._validateToken');

      // Log the endpoint being used
      final fullUrl = '${_apiService.baseUrl}${ApiConstants.userProfile}';
      developer.log('Validating token using endpoint: $fullUrl',
          name: 'AuthService._validateToken');

      // Attempt 1: Fetch user profile to validate token existence and basic validity.
      await _apiService.get(ApiConstants.userProfile);
      developer.log('Token validation step 1 successful (profile fetched).',
          name: 'AuthService._validateToken');

      // Attempt 2: Fetch full user details to populate _currentUser correctly.
      try {
        final userData = await _apiService.get(ApiConstants.userDetails);
        if (userData != null && userData is Map<String, dynamic>) {
          _currentUser = User.fromJson(userData);
          // Add more detailed logging
          developer.log(
              'User details fetched. Parsed User: ID=${_currentUser?.id}, Email=${_currentUser?.email}, Name=${_currentUser?.firstName}',
              name: 'AuthService._validateToken');
          notifyListeners(); // Notify listeners after updating user data
        } else {
          developer.log('Failed to parse user details from /users/me/.',
              name: 'AuthService._validateToken', level: 900);
          // Don't necessarily invalidate the token here, profile fetch succeeded.
        }
      } catch (userDetailsError) {
        developer.log(
            'Error fetching user details from /users/me/ after successful token validation: $userDetailsError',
            name: 'AuthService._validateToken',
            error: userDetailsError,
            level: 1000);
        // Token is still valid based on profile fetch, but user details might be stale/missing.
      }

      return true; // Token is considered valid if profile fetch succeeded.
    } on DioException catch (e) {
      // If fetching profile (the validation step) fails with 401, token is invalid/expired
      if (e.response?.statusCode == 401) {
        developer.log('Token validation failed (401 Unauthorized).',
            name: 'AuthService._validateToken', error: e);
        // Optionally attempt refresh here, but for now just invalidate
        await _secureStorage.delete(key: _accessTokenKey);
        await _secureStorage.delete(key: _refreshTokenKey);
        return false;
      }
      // Other errors during profile fetch might not mean the token is invalid
      // but indicate other issues (network, server error).
      // We might still consider the token potentially valid but log the error.
      developer.log(
          'Token validation check encountered non-401 error: ${e.response?.statusCode ?? e.message}',
          name: 'AuthService._validateToken',
          error: e);
      // Let's assume token is invalid for any DioException during validation for simplicity
      await _secureStorage.delete(key: _accessTokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);
      return false;
    } catch (e) {
      // Catch any other unexpected errors during validation
      developer.log('Unexpected error during token validation.',
          name: 'AuthService._validateToken', error: e);
      await _secureStorage.delete(key: _accessTokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);
      return false;
    }
  }

  // Debug method to help diagnose auth issues
  Future<Map<String, dynamic>> checkAuthDebug() async {
    try {
      final token = await _secureStorage.read(key: _accessTokenKey);
      if (token == null) {
        return {'authenticated': false, 'error': 'No token available'};
      }

      final result = <String, dynamic>{
        'token_available': token != null,
        'token_length': token.length,
        'token_preview':
            token.length > 10 ? '${token.substring(0, 10)}...' : token,
        'token_format_drf': 'Token $token', // DRF Token format
        'token_format_bearer': 'Bearer $token', // JWT format
        'verification_attempts': <String, dynamic>{},
      };

      // Attempt 1: Try with Django debug endpoint
      try {
        final debugResponse = await _apiService.get('/api/auth/debug/');
        result['verification_attempts']!['debug_endpoint'] = {
          'success': true,
          'response': debugResponse,
        };

        // If we get here, the token is valid
        result['authenticated'] = true;
        result['verification_method'] = 'debug_endpoint';
      } catch (e) {
        // Removed print statement
        result['verification_attempts']!['debug_endpoint'] = {
          'success': false,
          'error': e.toString(),
        };
      }

      // Attempt 2: Try fetching user profile if debug endpoint failed
      if (result['authenticated'] != true) {
        try {
          final profileResponse = await _apiService.get('/api/users/profile/');
          result['verification_attempts']!['profile_endpoint'] = {
            'success': true,
            'response_preview':
                profileResponse != null ? 'Data available' : 'No data',
          };

          // If we get here, the token is valid
          result['authenticated'] = true;
          result['verification_method'] = 'profile_endpoint';
        } catch (e) {
          // Removed print statement
          result['verification_attempts']!['profile_endpoint'] = {
            'success': false,
            'error': e.toString(),
          };
        }
      }

      // Attempt 3: Try with auth status endpoint
      if (result['authenticated'] != true) {
        try {
          final statusResponse = await _apiService.get('/api/auth/status/');
          result['verification_attempts']!['status_endpoint'] = {
            'success': true,
            'response': statusResponse,
          };

          // If we get here, the token is valid
          result['authenticated'] = true;
          result['verification_method'] = 'status_endpoint';
        } catch (e) {
          // Removed print statement
          result['verification_attempts']!['status_endpoint'] = {
            'success': false,
            'error': e.toString(),
          };
        }
      }

      return result;
    } catch (e) {
      return {'authenticated': false, 'error': 'Auth debug error: $e'};
    }
  }

  // Submit Questionnaire Data
  Future<bool> submitQuestionnaire(QuestionnaireData data) async {
    _isLoading = true;
    _error = null;
    notifyListeners(); // Notify loading state

    try {
      final submissionData = data.toJson();
      // Disabled general logs
      // developer.log('Submitting questionnaire data (original): $submissionData',
      //     name: 'AuthService');

      // Ensure health_conditions is present and uppercase
      List<String>? conditions = data.healthConditions; // Get original list
      if (conditions == null || conditions.isEmpty) {
        // If null or empty, default to ["NONE"]
        submissionData['health_conditions'] = ['NONE'];
        // Disabled general logs
        // developer.log('Health conditions was null/empty, setting to ["NONE"]',
        //     name: 'AuthService');
      } else {
        // If not empty, convert items to uppercase
        submissionData['health_conditions'] =
            conditions.map((c) => c.toUpperCase()).toList();
        // Disabled general logs
        // developer.log(
        //     'Transformed health conditions to uppercase: ${submissionData['health_conditions']}',
        //     name: 'AuthService');
      }

      // Convert boolean string values to actual booleans for backend
      final booleanFields = {
        'is_keto': data.isKeto,
        'is_intermittent_fasting': data.isIntermittentFasting,
        'is_physically_active': data.isPhysicallyActive,
        'has_home_equipment': data.hasHomeEquipment,
      };

      for (final entry in booleanFields.entries) {
        if (entry.value != null) {
          submissionData[entry.key] = entry.value?.toUpperCase() == 'YES';
        }
      }

      // Fix workout location mapping: Flutter sends 'BOTH' but backend expects 'HYBRID'
      if (submissionData['workout_location'] == 'BOTH') {
        submissionData['workout_location'] = 'HYBRID';
      }

      // Fix workout days mapping: Convert from display format to backend format
      final workoutDaysMapping = {
        '2-3 days': '2_3_DAYS',
        '4-5 days': '4_5_DAYS',
        '6-7 days': '6_7_DAYS',
      };

      if (submissionData['workout_days'] != null) {
        final mappedDays = workoutDaysMapping[submissionData['workout_days']];
        if (mappedDays != null) {
          submissionData['workout_days'] = mappedDays;
        }
      }

      // Fix age group mapping: Convert from display format to backend format
      final ageGroupMapping = {
        'Under 30': 'UNDER_30',
        '30-40': '30_40',
        '40+': '40_PLUS',
      };

      if (submissionData['age_group'] != null) {
        final mappedAge = ageGroupMapping[submissionData['age_group']];
        if (mappedAge != null) {
          submissionData['age_group'] = mappedAge;
        }
      }

      // Fix cooking preference mapping: Flutter sends 'WORK' but backend expects 'OUT'
      if (submissionData['cooking_preference'] == 'WORK') {
        submissionData['cooking_preference'] = 'OUT';
      }

      // Water intake is already in correct format (LESS_4, 4_8, MORE_8)

      // Fix fitness level mapping: Ensure proper case
      if (submissionData['fitness_level'] != null) {
        submissionData['fitness_level'] =
            submissionData['fitness_level'].toString().toUpperCase();
      }

      // Add local_date parameter (current date in YYYY-MM-DD format)
      // Use DateTime.now().toLocal() to ensure we're using the device's local time
      final now = DateTime.now().toLocal();
      final localDate =
          '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
      submissionData['local_date'] = localDate;

      // Also include timezone information to help server make correct calculations
      submissionData['timezone'] = now.timeZoneName;
      submissionData['timezone_offset'] = now.timeZoneOffset.inMinutes;

      // Add detailed logging for questionnaire submission
      print('======== QUESTIONNAIRE SUBMISSION ========');
      print('Sending local_date: $localDate');
      print('Timezone: ${now.timeZoneName}');
      print('Timezone offset: ${now.timeZoneOffset.inMinutes} minutes');
      print('Full submission data: $submissionData');
      print('=========================================');

      developer.log(
          'Sending local_date: $localDate with timezone: ${now.timeZoneName} (offset: ${now.timeZoneOffset.inMinutes} minutes)',
          name: 'AuthService');

      // AuthService already has an _apiService instance
      final response = await _apiService
          .submitQuestionnaire(submissionData); // Send modified data

      // Add detailed logging for server response
      print('======== SERVER RESPONSE ========');
      print('Response: $response');
      print('Response type: ${response.runtimeType}');
      print('Contains profile? ${response.containsKey('profile')}');
      print('Contains message? ${response.containsKey('message')}');
      if (response.containsKey('message')) {
        print('Message: ${response['message']}');
      }
      if (response.containsKey('profile')) {
        print('Profile: ${response['profile']}');
      }
      print('================================');

      // Disabled general logs
      // developer.log('Questionnaire submission response: $response',
      //     name: 'AuthService');

      // Assuming the API returns a success indicator or specific data on success
      // Adjust this check based on the actual API response structure
      // Checking if the response contains a 'profile' key as an indicator of success based on backend view
      if (response.containsKey('profile')) {
        // Disabled general logs
        // developer.log('Questionnaire submitted successfully via AuthService.',
        //     name: 'AuthService');
        _isLoading = false;

        // Reset workout and meal plan progress to day 1 with current date as the start date
        final userProgressProvider = Provider.of<UserProgressProvider>(
            NavigationService.navigatorKey.currentContext!,
            listen: false);

        // Use current date as the start date
        final now = DateTime.now().toLocal();

        // Reset both workout and meal plan progress
        await userProgressProvider.resetWorkoutPlanProgress(startDate: now);
        await userProgressProvider.resetMealPlanProgress(startDate: now);

        // Add detailed logging for progress reset
        print('======== PROGRESS RESET ========');
        print('Reset date: $now');
        print(
            'Formatted date: ${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}');
        print('Timezone: ${now.timeZoneName}');
        print('Timezone offset: ${now.timeZoneOffset.inMinutes} minutes');
        print('===============================');

        // Keep this log as it's related to the local date issue
        developer.log(
            'Reset workout and meal plan progress to day 1 with start date: $now',
            name: 'AuthService');

        // Fetch updated profile to reflect questionnaire completion status
        await getUserProfile(); // Refresh profile data
        notifyListeners(); // Notify after profile update
        return true;
      } else {
        _error = response['message'] ?? 'Failed to submit questionnaire.';
        // Disabled general logs
        // developer.log('Questionnaire submission failed: $_error',
        //     name: 'AuthService', level: 900);
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'Error submitting questionnaire: ${e.toString()}';
      // Disabled general logs
      // developer.log('Questionnaire submission exception: $_error',
      //     name: 'AuthService', error: e, level: 1000);
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Clear error state
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
