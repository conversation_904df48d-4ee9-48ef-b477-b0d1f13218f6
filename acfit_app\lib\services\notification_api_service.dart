import '../services/api_service.dart';
import '../constants/api_constants.dart';
import '../models/notification_log.dart';
import '../models/notification_settings.dart';
import '../utils/logger.dart';

class NotificationApiService {
  static final ApiService _apiService = ApiService();

  /// Get user's notification logs
  static Future<List<NotificationLog>> getNotificationLogs(
      {int limit = 50}) async {
    try {
      Logger.log('Fetching notification logs', tag: 'NotificationApiService');

      final response = await _apiService.get(
        '/notifications/logs/',
        queryParameters: {'limit': limit.toString()},
      );

      if (response != null && response['results'] != null) {
        final List<dynamic> results = response['results'];
        return results.map((json) => NotificationLog.fromJson(json)).toList();
      }

      return [];
    } catch (e) {
      Logger.log('Error fetching notification logs: $e',
          tag: 'NotificationApiService');
      return [];
    }
  }

  /// Get unread notification count
  static Future<int> getUnreadCount() async {
    try {
      final response =
          await _apiService.get('/notifications/logs/unread_count/');
      return response?['unread_count'] ?? 0;
    } catch (e) {
      Logger.log('Error fetching unread count: $e',
          tag: 'NotificationApiService');
      return 0;
    }
  }

  /// Mark notifications as read
  static Future<bool> markAsRead(List<String> notificationIds) async {
    try {
      Logger.log('Marking ${notificationIds.length} notifications as read',
          tag: 'NotificationApiService');

      final response = await _apiService.post(
        '/notifications/logs/mark_as_read/',
        data: {'notification_ids': notificationIds},
      );

      return response != null;
    } catch (e) {
      Logger.log('Error marking notifications as read: $e',
          tag: 'NotificationApiService');
      return false;
    }
  }

  /// Get user's notification settings
  static Future<NotificationSettings?> getNotificationSettings() async {
    try {
      Logger.log('Fetching notification settings',
          tag: 'NotificationApiService');

      final response = await _apiService.get('/notifications/settings/me/');

      if (response != null) {
        return NotificationSettings.fromJson(response);
      }

      return null;
    } catch (e) {
      Logger.log('Error fetching notification settings: $e',
          tag: 'NotificationApiService');
      return null;
    }
  }

  /// Update user's notification settings
  static Future<NotificationSettings?> updateNotificationSettings(
    NotificationSettings settings,
  ) async {
    try {
      Logger.log('Updating notification settings',
          tag: 'NotificationApiService');

      final response = await _apiService.patch(
        '/notifications/settings/update_settings/',
        data: settings.toJson(),
      );

      if (response != null) {
        return NotificationSettings.fromJson(response);
      }

      return null;
    } catch (e) {
      Logger.log('Error updating notification settings: $e',
          tag: 'NotificationApiService');
      return null;
    }
  }

  /// Test reminder notifications (for development)
  static Future<bool> testReminderNotification(String type) async {
    try {
      Logger.log('Testing $type notification', tag: 'NotificationApiService');

      final response = await _apiService.post(
        '/notifications/test/reminders/',
        data: {'type': type},
      );

      return response != null;
    } catch (e) {
      Logger.log('Error testing notification: $e',
          tag: 'NotificationApiService');
      return false;
    }
  }

  /// Sync notifications from server and show local notifications
  static Future<void> syncAndShowNotifications() async {
    try {
      Logger.log('Syncing notifications from server',
          tag: 'NotificationApiService');

      // Get recent notifications
      final notifications = await getNotificationLogs(limit: 10);

      // Show unread notifications as local notifications
      for (final notification in notifications) {
        if (notification.isUnread) {
          await _showLocalNotification(notification);
        }
      }
    } catch (e) {
      Logger.log('Error syncing notifications: $e',
          tag: 'NotificationApiService');
    }
  }

  /// Show a local notification for a server notification
  static Future<void> _showLocalNotification(
      NotificationLog notification) async {
    try {
      // This would integrate with your existing LocalNotificationService
      // For now, just log that we would show the notification
      Logger.log(
          'Would show local notification: ${notification.title} - ${notification.message}',
          tag: 'NotificationApiService');
    } catch (e) {
      Logger.log('Error showing local notification: $e',
          tag: 'NotificationApiService');
    }
  }
}
