from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404, render
from django.http import HttpResponse
from .models import (
    NotificationLog, UserNotificationSettings, 
    AdminNotificationCampaign, NotificationTemplate
)
from .serializers import (
    NotificationLogSerializer, UserNotificationSettingsSerializer,
    AdminNotificationCampaignSerializer, CreateAdminNotificationSerializer,
    NotificationTemplateSerializer, MarkAsReadSerializer
)
from .services import NotificationService, AdminNotificationService, ReminderService
from workouts.models import WorkoutPlan
from meals.models import MealPlan
from accounts.models import UserProfile

User = get_user_model()


class NotificationLogViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for user notifications"""
    serializer_class = NotificationLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return NotificationLog.objects.filter(user=self.request.user).order_by('-created_at')
    
    @action(detail=False, methods=['post'])
    def mark_as_read(self, request):
        """Mark notifications as read"""
        serializer = MarkAsReadSerializer(data=request.data)
        if serializer.is_valid():
            notification_ids = serializer.validated_data['notification_ids']
            updated_count = 0
            
            for notification_id in notification_ids:
                if NotificationService.mark_notification_as_read(notification_id, request.user):
                    updated_count += 1
            
            return Response({
                'message': f'{updated_count} notifications marked as read',
                'updated_count': updated_count
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def unread_count(self, request):
        """Get count of unread notifications"""
        count = NotificationLog.objects.filter(
            user=request.user,
            status__in=['PENDING', 'SENT']
        ).count()
        
        return Response({'unread_count': count})


class UserNotificationSettingsViewSet(viewsets.ModelViewSet):
    """ViewSet for user notification settings"""
    serializer_class = UserNotificationSettingsSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserNotificationSettings.objects.filter(user=self.request.user)
    
    def get_object(self):
        """Get or create user notification settings"""
        settings, created = UserNotificationSettings.objects.get_or_create(
            user=self.request.user
        )
        return settings
    
    @action(detail=False, methods=['get'])
    def me(self, request):
        """Get current user's notification settings"""
        settings = self.get_object()
        serializer = self.get_serializer(settings)
        return Response(serializer.data)
    
    @action(detail=False, methods=['patch'])
    def update_settings(self, request):
        """Update current user's notification settings"""
        settings = self.get_object()
        serializer = self.get_serializer(settings, data=request.data, partial=True)
        
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AdminNotificationCampaignViewSet(viewsets.ModelViewSet):
    """ViewSet for admin notification campaigns"""
    serializer_class = AdminNotificationCampaignSerializer
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]
    
    def get_queryset(self):
        return AdminNotificationCampaign.objects.all().order_by('-created_at')
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def send(self, request, pk=None):
        """Send the notification campaign"""
        campaign = self.get_object()
        
        if campaign.is_sent:
            return Response(
                {'error': 'Campaign has already been sent'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        success = AdminNotificationService.send_admin_notification(campaign.id)
        
        if success:
            # Refresh the campaign object to get updated stats
            campaign.refresh_from_db()
            serializer = self.get_serializer(campaign)
            return Response({
                'message': 'Campaign sent successfully',
                'campaign': serializer.data
            })
        else:
            return Response(
                {'error': 'Failed to send campaign'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def targeting_options(self, request):
        """Get available targeting options"""
        workout_plans = WorkoutPlan.objects.all().values('id', 'name')
        meal_plans = MealPlan.objects.all().values('id', 'name')
        
        # Get available questionnaire fields
        questionnaire_fields = {
            'gender': [choice[0] for choice in UserProfile._meta.get_field('gender').choices],
            'age_group': [choice[0] for choice in UserProfile._meta.get_field('age_group').choices],
            'primary_fitness_goal': [choice[0] for choice in UserProfile._meta.get_field('primary_fitness_goal').choices],
            'fitness_level': [choice[0] for choice in UserProfile._meta.get_field('fitness_level').choices],
            'workout_location': [choice[0] for choice in UserProfile._meta.get_field('workout_location').choices],
            'workout_days': [choice[0] for choice in UserProfile._meta.get_field('workout_days').choices],
        }
        
        return Response({
            'workout_plans': list(workout_plans),
            'meal_plans': list(meal_plans),
            'questionnaire_fields': questionnaire_fields,
            'target_types': AdminNotificationCampaign.TARGET_TYPES
        })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, permissions.IsAdminUser])
def create_and_send_notification(request):
    """Create and immediately send a notification campaign"""
    serializer = CreateAdminNotificationSerializer(data=request.data)
    
    if serializer.is_valid():
        # Create campaign
        campaign = AdminNotificationCampaign.objects.create(
            created_by=request.user,
            **serializer.validated_data
        )
        
        # Send immediately
        success = AdminNotificationService.send_admin_notification(campaign.id)
        
        if success:
            campaign.refresh_from_db()
            response_serializer = AdminNotificationCampaignSerializer(campaign)
            return Response({
                'message': 'Notification sent successfully',
                'campaign': response_serializer.data
            })
        else:
            return Response(
                {'error': 'Failed to send notification'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def test_reminder_notifications(request):
    """Test endpoint to send reminder notifications (for development)"""
    user = request.user
    notification_type = request.data.get('type', 'WORKOUT_REMINDER')
    
    if notification_type == 'WORKOUT_REMINDER':
        success = ReminderService.send_workout_reminder(user, "Test Workout")
    elif notification_type == 'MEAL_REMINDER':
        success = ReminderService.send_meal_reminder(user, "Test Meal")
    elif notification_type == 'HYDRATION_REMINDER':
        success = ReminderService.send_hydration_reminder(user)
    elif notification_type == 'WEEKLY_MOTIVATION':
        success = ReminderService.send_weekly_motivation(user)
    else:
        return Response(
            {'error': 'Invalid notification type'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    if success:
        return Response({'message': f'{notification_type} sent successfully'})
    else:
        return Response(
            {'error': 'Failed to send notification'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, permissions.IsAdminUser])
def notification_identifiers_help(request):
    """Get help documentation for notification identifiers"""
    identifiers = {
        'user_identifiers': {
            '{user.name}': 'User\'s first name or email username',
            '{user.first_name}': 'User\'s first name',
            '{user.last_name}': 'User\'s last name',
            '{user.email}': 'User\'s email address',
        },
        'plan_identifiers': {
            '{user.workout_plan}': 'User\'s current active workout plan name',
            '{user.meal_plan}': 'User\'s current active meal plan name',
        },
        'profile_identifiers': {
            '{user.fitness_goal}': 'User\'s primary fitness goal',
            '{user.fitness_level}': 'User\'s fitness level',
            '{user.age_group}': 'User\'s age group',
        },
        'examples': [
            'Hey {user.name}! Time to start your {user.workout_plan} workout!',
            'Hi {user.first_name}, your {user.meal_plan} meal is ready!',
            'Great job {user.name}! Your {user.fitness_goal} journey is going amazing!',
        ]
    }
    
    return Response(identifiers)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, permissions.IsAdminUser])
def admin_documentation(request):
    """Render the admin documentation page"""
    return render(request, 'notifications/admin_documentation.html')
