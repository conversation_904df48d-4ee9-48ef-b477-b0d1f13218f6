"""
URL configuration for acfit_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include, re_path
from django.http import JsonResponse

from django.conf import settings
from django.conf.urls.static import static
from rest_framework import permissions, routers
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from admin_site.admin import acfit_admin_site
# from health_check import health_check # Removed as per user request
# from rest_framework.authtoken.views import obtain_auth_token # Removed - Using SimpleJWT
from accounts.views import (
    UserViewSet, UserProfileViewSet,
    UserScoreBreakdownView, # Keep views used directly below
    # CSRFTokenView, # Moved to accounts.urls
    UserScoreAPIView, # UserProgressAPIView, UserProfileAPIView, UserCaloriesView - Check if used elsewhere or remove
    get_user_calories
)
# Import the direct_complete_workout_session function
from workouts.views.direct_complete import direct_complete_workout_session
from workouts.views.test_direct_complete import test_direct_complete
from workouts.views.emergency_complete import emergency_complete_workout
# Remove imports for views now handled in accounts.urls
# from accounts.auth_views import (
#     LoginView, LogoutView, TokenView, register, verify_email, resend_verification
# )
# from accounts.auth_jwt import (
#     CustomTokenRefreshView,
# )
# from rest_framework_simplejwt.views import TokenVerifyView
# from workouts.views import get_todays_workout, get_upcoming_workouts # Removed - Handled in workouts.urls
# from meals.views import get_todays_meals, get_todays_hydration, update_hydration, get_hydration_history # Removed - Handled in meals.urls

router = routers.DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'profiles', UserProfileViewSet)

schema_view = get_schema_view(
    openapi.Info(
        title="ACFIT API",
        default_version='v1',
        description="ACFIT API documentation",
        terms_of_service="https://www.google.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],
)

urlpatterns = [
    # Health check endpoint (removed as per user request)
    # path('health/', health_check, name='health-check'),

    path('admin/', admin.site.urls),
    path('acfit-admin/', acfit_admin_site.urls),


    # API routes
    path('api/', include(router.urls)),

    # Define specific user endpoints BEFORE including accounts.urls to avoid conflicts
    path('api/accounts/users/score/', UserScoreAPIView.as_view(), name='accounts-user-score'),
    path('api/accounts/users/score/breakdown/', UserScoreBreakdownView.as_view(), name='accounts-score-breakdown'),
    path('api/accounts/users/calories/', get_user_calories, name='user-calories'),
    # Note: profile endpoint is handled by the router ('/api/profiles/me/')

    # Include app-specific URLs
    path('api/accounts/', include('accounts.urls')), # General accounts URLs
    path('api/workouts/', include('workouts.urls')),
    # Add direct paths to the direct-complete endpoint (both with and without trailing slash)
    path('api/workouts/session-logs/direct-complete/', direct_complete_workout_session, name='direct-complete-workout-session'),
    path('api/workouts/session-logs/direct-complete', direct_complete_workout_session, name='direct-complete-workout-session-no-slash'),
    # Add test endpoint for direct completion
    path('api/workouts/session-logs/test-direct-complete/', test_direct_complete, name='test-direct-complete'),
    # Add emergency completion endpoint
    path('api/workouts/session-logs/emergency-complete/', emergency_complete_workout, name='emergency-complete-workout'),
    path('api/workouts/session-logs/emergency-complete', emergency_complete_workout, name='emergency-complete-workout-no-slash'),
    path('api/meals/', include('meals.urls')),
    path('api/support/', include('support.urls')),
    path('api/activity/', include('activity_logs.urls')),
    path('api/products/', include('products.urls')),
    path('api/notifications/', include('notifications.urls')),

    # Authentication URLs are now included via 'accounts.urls'
    # path('api/auth/register/', register, name='register'), # Moved
    # path('api/auth/verify-email/<str:uidb64>/<str:token>/', verify_email, name='verify_email'), # Moved
    # path('api/auth/resend-verification/', resend_verification, name='resend_verification'), # Moved
    # path('api/auth/login/', LoginView.as_view(), name='login'), # Moved
    # path('api/auth/logout/', LogoutView.as_view(), name='logout'), # Moved
    # path('api/auth/token/', TokenView.as_view(), name='token'), # Moved
    # path('api/auth/token/refresh/', CustomTokenRefreshView.as_view(), name='token-refresh'), # Moved
    # path('api/auth/token/verify/', TokenVerifyView.as_view(), name='token-verify'), # Moved
    # path('api/csrf-token/', CSRFTokenView.as_view(), name='csrf-token'), # Moved

    # Workout endpoints are now included via 'workouts.urls'
    # path('api/workouts/today/', get_todays_workout, name='workouts-today'), # Removed redundant path
    # path('api/workouts/upcoming/', get_upcoming_workouts, name='workouts-upcoming'), # Removed redundant path

    # Meal endpoints are now included via 'meals.urls'
    # path('api/meals/today/', get_todays_meals, name='meals-today'), # Removed redundant path
    # path('api/meals/hydration/today/', get_todays_hydration, name='meals-hydration-today'), # Removed redundant path
    # path('api/meals/hydration/update/', update_hydration, name='meals-update-hydration'), # Removed - Should be handled by HydrationLogViewSet in meals.urls
    # path('api/meals/hydration/history/', get_hydration_history, name='meals-hydration-history'), # Removed - Handled in meals.urls

    # API Documentation
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),

    # Markdown Documentation removed
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
