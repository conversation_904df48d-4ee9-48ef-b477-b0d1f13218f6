import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:developer' as developer;
import '../../services/auth_service.dart'; // Assuming this service exists
import '../../services/navigation_service.dart'; // Assuming this service exists

// Blinking cursor widget for OTP input
class BlinkingCursor extends StatefulWidget {
  const BlinkingCursor({Key? key}) : super(key: key);

  @override
  State<BlinkingCursor> createState() => _BlinkingCursorState();
}

class _BlinkingCursorState extends State<BlinkingCursor>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animation,
      child: Container(
        width: 2,
        height: 24,
        color: const Color.fromRGBO(249, 115, 22, 1),
      ),
    );
  }
}

enum VerificationType {
  emailVerification,
  passwordReset,
}

// Add the necessary mixin for animations
class VerificationCodeScreen extends StatefulWidget {
  final String email;
  final VerificationType verificationType;
  final Function(String code)? onVerificationComplete;

  const VerificationCodeScreen({
    Key? key,
    required this.email,
    required this.verificationType,
    this.onVerificationComplete,
  }) : super(key: key);

  @override
  State<VerificationCodeScreen> createState() => _VerificationCodeScreenState();
}

// Add SingleTickerProviderStateMixin
class _VerificationCodeScreenState extends State<VerificationCodeScreen>
    with SingleTickerProviderStateMixin {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;
  final TextEditingController _combinedController = TextEditingController();

  // Number of digits in the verification code
  late int _codeLength;

  bool _isLoading = false;
  String? _errorMessage;
  bool _isResending = false;

  // Focus node for the hidden input field
  final FocusNode _inputFocusNode = FocusNode();

  // Animation variables
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Set code length based on verification type
    _codeLength =
        widget.verificationType == VerificationType.passwordReset ? 6 : 4;

    // Initialize controllers and focus nodes based on code length
    _controllers =
        List.generate(_codeLength, (index) => TextEditingController());
    _focusNodes = List.generate(_codeLength, (index) => FocusNode());

    _combinedController.addListener(_updateIndividualControllers);

    // Initialize AnimationController
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800), // Adjust duration as needed
      vsync: this,
    );

    // Define fade animation
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeIn),
    );

    // Define slide animation (slides from slightly below)
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );

    // Start the animation when the screen is built
    _controller.forward();

    // Auto-focus the input field when the screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Add a slight delay before showing the keyboard to allow entrance animation to play
      Future.delayed(const Duration(milliseconds: 400), () {
        _showKeyboard();
      });

      // Auto-fill OTP for development (after a longer delay to let animations finish)
      Future.delayed(const Duration(milliseconds: 2000), () {
        _autoFillOTP();
      });
    });
  }

  // Method to show keyboard and focus on the input field
  void _showKeyboard() {
    if (_inputFocusNode.canRequestFocus) {
      _inputFocusNode.requestFocus();
    }
  }

  // Auto-fill OTP for development purposes
  Future<void> _autoFillOTP() async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);

      // Get the correct OTP from the backend
      String? correctOTP = await authService.getLatestVerificationCode(
          widget.email,
          widget.verificationType == VerificationType.passwordReset
              ? 'password'
              : 'email');

      if (correctOTP != null && correctOTP.isNotEmpty && mounted) {
        // Fill the OTP automatically
        _combinedController.text = correctOTP;

        // Show a subtle indication that OTP was auto-filled
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('🔧 OTP auto-filled for development'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      // Silently fail - this is just a development convenience feature
      developer.log('Auto-fill OTP failed: $e', name: 'VerificationCodeScreen');
    }
  }

  @override
  void dispose() {
    _combinedController.removeListener(_updateIndividualControllers);
    _combinedController.dispose();
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    _inputFocusNode.dispose();
    _controller.dispose(); // Dispose the animation controller
    super.dispose();
  }

  void _updateIndividualControllers() {
    final text = _combinedController.text;
    if (text.length <= _codeLength) {
      for (int i = 0; i < _codeLength; i++) {
        // Only update if the text has changed for this controller
        if (i < text.length && _controllers[i].text != text[i]) {
          setState(() {
            // setState needed to trigger rebuild for AnimatedContainer
            _controllers[i].text = text[i];
          });
        } else if (i >= text.length && _controllers[i].text.isNotEmpty) {
          setState(() {
            // setState needed to trigger rebuild for AnimatedContainer
            _controllers[i].text = '';
          });
        }
      }

      // Auto-submit when all digits are entered
      if (text.length == _codeLength) {
        _verifyCode();
      }
    }
  }

  String get _title {
    switch (widget.verificationType) {
      case VerificationType.emailVerification:
        return 'Email Verification';
      case VerificationType.passwordReset:
        return 'Password Reset';
    }
  }

  Future<void> _verifyCode() async {
    // Get code from combined controller
    final code = _combinedController.text;

    if (code.length != _codeLength) {
      setState(() {
        _errorMessage = 'Please enter all $_codeLength digits of the code';
      });
      return;
    }

    // Hide keyboard before showing loading indicator
    _inputFocusNode.unfocus();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      bool success = false;

      switch (widget.verificationType) {
        case VerificationType.emailVerification:
          success = await authService.verifyEmailWithCode(widget.email, code);
          break;
        case VerificationType.passwordReset:
          success =
              await authService.verifyPasswordResetCode(widget.email, code);
          break;
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (success) {
          if (widget.onVerificationComplete != null) {
            widget.onVerificationComplete!(code);
          } else {
            switch (widget.verificationType) {
              case VerificationType.emailVerification:
                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Email verified successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
                // No need to navigate here - the auth service will handle navigation
                // after storing the token and setting the user as authenticated
                break;
              case VerificationType.passwordReset:
                // Navigate to password reset screen instead of the removed new_password_screen
                NavigationService.navigateToReplacementNamed(
                  NavigationService.passwordReset,
                );
                break;
            }
          }
        } else {
          // Clear the input field for retry and show keyboard again
          _combinedController.clear();
          _showKeyboard(); // Show keyboard again on error
          setState(() {
            _errorMessage = authService.error ??
                'Invalid verification code. Please try again.';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        // Clear the input field for retry and show keyboard again
        _combinedController.clear();
        _showKeyboard(); // Show keyboard again on error
        setState(() {
          _isLoading = false;
          _errorMessage = 'An error occurred: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _resendCode() async {
    // Hide keyboard before resending
    _inputFocusNode.unfocus();

    setState(() {
      _isResending = true;
      _errorMessage = null;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      bool success = false;

      switch (widget.verificationType) {
        case VerificationType.emailVerification:
          success = await authService.resendVerificationEmail(widget.email);
          break;
        case VerificationType.passwordReset:
          success = await authService.resetPassword(widget.email);
          break;
      }

      if (mounted) {
        setState(() {
          _isResending = false;
        });

        if (success) {
          // Clear the input field after successful resend
          _combinedController.clear();
          // Show keyboard again after successful resend
          _showKeyboard();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  Text('Verification code has been resent to ${widget.email}'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          // Show keyboard again on resend failure
          _showKeyboard();
          setState(() {
            _errorMessage =
                authService.error ?? 'Failed to resend verification code';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        // Show keyboard again on error
        _showKeyboard();
        setState(() {
          _isResending = false;
          _errorMessage = 'An error occurred: ${e.toString()}';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Wrap the main content in FadeTransition and SlideTransition
    return Scaffold(
      appBar: AppBar(
        title: Text(_title),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 40),
                  // Animate Title Text
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Text(
                      'Enter $_codeLength digit OTP Code!',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: Color.fromRGBO(16, 17, 20, 1),
                        fontFamily: 'Work Sans',
                        fontSize: 30,
                        fontWeight: FontWeight.normal,
                        height: 1.27,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Animate Description Text
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Text(
                      'Please enter the $_codeLength digit OTP code we sent to your email! 🙏',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: Color.fromRGBO(103, 107, 116, 1),
                        fontFamily: 'Work Sans',
                        fontSize: 16,
                        fontWeight: FontWeight.normal,
                        height: 1.5,
                      ),
                    ),
                  ),
                  const SizedBox(height: 48),
                  // Hidden combined input field for better UX
                  // This field is positioned to overlay with the OTP boxes
                  // but is transparent to allow the boxes to be visible
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      // Invisible text field that captures input
                      Container(
                        width:
                            _codeLength * 58.0, // Width to match the OTP boxes
                        height: _codeLength == 6
                            ? 50
                            : 70, // Height to match the OTP boxes
                        alignment: Alignment.center,
                        child: TextField(
                          controller: _combinedController,
                          focusNode: _inputFocusNode,
                          keyboardType: TextInputType.number,
                          maxLength: _codeLength,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                              color: Colors.transparent, height: 0),
                          decoration: const InputDecoration(
                            counterText: '',
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            contentPadding: EdgeInsets.zero,
                          ),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                          showCursor:
                              false, // Hide the cursor as we'll show a custom one
                        ),
                      ),
                    ],
                  ),

                  // Verification code display fields with staggered animation
                  Container(
                    alignment: Alignment.center,
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          _codeLength,
                          (index) => AnimatedBuilder(
                              // Use AnimatedBuilder for individual box animations
                              animation: _controller,
                              builder: (context, child) {
                                // Calculate delay and interval for staggered effect
                                final double stagger = 1.0 / _codeLength;
                                final double start = stagger * index;
                                final double end = start + stagger;

                                final CurvedAnimation staggeredAnimation =
                                    CurvedAnimation(
                                  parent: _controller,
                                  curve: Interval(start, end,
                                      curve: Curves.easeOut),
                                );

                                // Apply scale and fade animations to each box
                                return FadeTransition(
                                  opacity: Tween<double>(begin: 0.0, end: 1.0)
                                      .animate(staggeredAnimation),
                                  child: ScaleTransition(
                                    scale: Tween<double>(begin: 0.8, end: 1.0)
                                        .animate(staggeredAnimation),
                                    child: GestureDetector(
                                      onTap: () {
                                        // When tapping on a box, show keyboard and focus on the input
                                        _showKeyboard();
                                      },
                                      // Use AnimatedContainer for animating border changes
                                      child: AnimatedContainer(
                                        duration: const Duration(
                                            milliseconds:
                                                200), // Animation duration for container properties
                                        curve: Curves.easeInOut,
                                        width: _codeLength == 6 ? 50 : 70,
                                        height: _codeLength == 6 ? 50 : 70,
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 4),
                                        decoration: BoxDecoration(
                                          color: const Color.fromRGBO(
                                              243, 243, 243, 1),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                            // Show orange border for filled boxes or the current cursor position
                                            color: _controllers[index]
                                                    .text
                                                    .isNotEmpty
                                                ? const Color.fromRGBO(
                                                    249,
                                                    115,
                                                    22,
                                                    1) // Orange border for filled fields
                                                : (_inputFocusNode.hasFocus &&
                                                        index ==
                                                            _combinedController
                                                                .text.length)
                                                    ? const Color.fromRGBO(
                                                        249,
                                                        115,
                                                        22,
                                                        0.7) // Lighter orange for cursor position
                                                    : const Color.fromRGBO(
                                                        229,
                                                        231,
                                                        235,
                                                        1), // Default gray
                                            width: _controllers[index]
                                                        .text
                                                        .isNotEmpty ||
                                                    (_inputFocusNode.hasFocus &&
                                                        index ==
                                                            _combinedController
                                                                .text.length)
                                                ? 2
                                                : 1,
                                          ),
                                          // Add subtle shadow for better visual appearance
                                          boxShadow: _controllers[index]
                                                      .text
                                                      .isNotEmpty ||
                                                  (_inputFocusNode.hasFocus &&
                                                      index ==
                                                          _combinedController
                                                              .text.length)
                                              ? [
                                                  const BoxShadow(
                                                    color: Color.fromRGBO(
                                                        249, 115, 22, 0.04),
                                                    blurRadius: 4,
                                                    offset: Offset(0, 2),
                                                  )
                                                ]
                                              : null,
                                        ),
                                        child: Center(
                                          child: _controllers[index]
                                                  .text
                                                  .isNotEmpty
                                              ? Text(
                                                  _controllers[index].text,
                                                  style: const TextStyle(
                                                    fontSize: 24,
                                                    fontWeight: FontWeight.bold,
                                                    color: Color.fromRGBO(
                                                        16, 17, 20, 1),
                                                  ),
                                                )
                                              : (_inputFocusNode.hasFocus &&
                                                      index ==
                                                          _combinedController
                                                              .text.length)
                                                  ? const BlinkingCursor() // Show blinking cursor in the current position
                                                  : const SizedBox(), // Empty for unfilled boxes
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              }),
                        ),
                      ),
                    ),
                  ),

                  // We've removed the cursor position indicator text
                  // The cursor is now visually indicated by the box styling
                  const SizedBox(height: 24),
                  // Error message with Fade-in animation
                  AnimatedOpacity(
                    opacity: _errorMessage != null ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 300),
                    child: _errorMessage != null
                        ? Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              color: const Color.fromRGBO(255, 237, 240, 1),
                              border: Border.all(
                                color: const Color.fromRGBO(248, 62, 89, 1),
                                width: 1,
                              ),
                            ),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.error_outline,
                                  color: Color.fromRGBO(248, 62, 89, 1),
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    _errorMessage!,
                                    style: const TextStyle(
                                      color: Color.fromRGBO(248, 62, 89, 1),
                                      fontFamily: 'Work Sans',
                                      fontSize: 14,
                                      fontWeight: FontWeight.normal,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : const SizedBox
                            .shrink(), // Use SizedBox.shrink when hidden
                  ),
                  const SizedBox(height: 64),
                  // Continue button (inherits main Fade/Slide animation, no specific button animation needed unless custom)
                  ElevatedButton(
                    onPressed: _isLoading ? null : _verifyCode,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color.fromRGBO(16, 17, 20, 1),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(19),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                              strokeWidth: 2,
                            ),
                          )
                        : const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Continue',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontFamily: 'Work Sans',
                                  fontSize: 16,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                              SizedBox(width: 16),
                              Icon(
                                Icons.arrow_forward,
                                color: Colors.white,
                              ),
                            ],
                          ),
                  ),
                  const SizedBox(height: 64),
                  // Resend code (inherits main Fade/Slide animation)
                  GestureDetector(
                    onTap: _isResending ? null : _resendCode,
                    child: Center(
                      child: _isResending
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                              ),
                            )
                          : const Text(
                              'Didn\'t receive your OTP? Send again',
                              style: TextStyle(
                                color: Color.fromRGBO(16, 17, 20, 1),
                                fontFamily: 'Work Sans',
                                fontSize: 14,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
