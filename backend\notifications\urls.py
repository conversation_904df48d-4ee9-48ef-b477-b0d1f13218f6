from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'logs', views.NotificationLogViewSet, basename='notification-log')
router.register(r'settings', views.UserNotificationSettingsViewSet, basename='notification-settings')
router.register(r'admin/campaigns', views.AdminNotificationCampaignViewSet, basename='admin-campaign')

urlpatterns = [
    path('', include(router.urls)),
    path('admin/send/', views.create_and_send_notification, name='create-and-send-notification'),
    path('admin/identifiers-help/', views.notification_identifiers_help, name='notification-identifiers-help'),
    path('admin/documentation/', views.admin_documentation, name='admin-documentation'),
    path('test/reminders/', views.test_reminder_notifications, name='test-reminder-notifications'),
]
