# models.py
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _
from .managers import CustomUserManager
from . import constants # Import the new constants file
from multiselectfield import MultiSelectField
import uuid
from django.conf import settings
from django.utils import timezone
from datetime import timedelta, date, datetime # Ensure date and datetime are imported
from django.db.models import F # Import F expression
import logging
import pytz # Add pytz import
# Keep top-level imports commented to avoid potential circular dependency
# from meals.models import UserMealPlan
# from workouts.models import WorkoutLog, WorkoutSessionLog, WorkoutSession

logger = logging.getLogger(__name__)

# Common choice constants moved to accounts/constants.py

class User(AbstractUser):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = CustomUserManager()

    def __str__(self):
        return self.email

class UserProfile(models.Model):
    """Extended user profile information"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='userprofile')

    # Basic Information
    gender = models.CharField(max_length=10, choices=constants.GENDER_CHOICES, default='M')
    height = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    weight = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    initial_weight = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    age = models.IntegerField(null=True, blank=True)
    age_group = models.CharField(max_length=20, choices=constants.AGE_GROUP_CHOICES, default='UNDER_30')
    profile_picture = models.ImageField(upload_to='profile_pictures/', null=True, blank=True) # Adding field back again
    timezone = models.CharField(max_length=63, default='UTC', help_text='User\'s local timezone (e.g., America/New_York)') # New Field

    # Health and Goals
    health_conditions = MultiSelectField(
        choices=constants.HEALTH_CONDITIONS,
        max_choices=len(constants.HEALTH_CONDITIONS),
        default=['NONE'],
        blank=True
    )
    fitness_goal = models.CharField(max_length=20, choices=constants.USER_PROFILE_FITNESS_GOALS, default='MAINTAIN_HEALTH')

    # Diet Preferences
    is_keto = models.BooleanField(default=False)
    is_intermittent_fasting = models.BooleanField(default=False)
    cooking_preference = models.CharField(max_length=10, choices=constants.COOKING_PREFERENCE_CHOICES, default='HOME')
    water_intake = models.CharField(max_length=10, choices=constants.WATER_INTAKE_CHOICES, default='4_8')

    # Workout Preferences
    is_physically_active = models.BooleanField(default=True)
    workout_location = models.CharField(max_length=20, choices=constants.WORKOUT_LOCATION_CHOICES, default='HOME')
    workout_days = models.CharField(max_length=10, choices=constants.WORKOUT_DAYS_CHOICES, default='2_3_DAYS')
    fitness_level = models.CharField(max_length=20, choices=constants.FITNESS_LEVEL_CHOICES, default='BEGINNER')
    has_home_equipment = models.BooleanField(default=False)

    # Plan Information
    plan_start_date = models.DateField(null=True, blank=True, help_text="The date the user's current active plan started")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.email}'s profile"

class UserProgress(models.Model):
    """Track overall user progress across all plans"""
    user = models.OneToOneField(UserProfile, on_delete=models.CASCADE, related_name='progress')
    start_date = models.DateTimeField(auto_now_add=True)
    total_workouts_completed = models.IntegerField(default=0)
    total_workouts_planned = models.IntegerField(default=0)
    total_calories_burned = models.IntegerField(default=0)
    total_exercise_minutes = models.IntegerField(default=0)
    total_steps = models.IntegerField(default=0)
    total_meals_completed = models.IntegerField(default=0)
    total_meals_planned = models.IntegerField(default=0)
    total_calories_consumed = models.IntegerField(default=0)
    total_protein_consumed = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_carbs_consumed = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_fat_consumed = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    last_workout_date = models.DateField(null=True, blank=True)
    last_meal_date = models.DateField(null=True, blank=True)
    # streak_days = models.IntegerField(default=0) # Removed - Redundant
    current_streak = models.IntegerField(default=0)
    longest_streak = models.IntegerField(default=0)
    # Track current day in workout and meal plans
    workout_day = models.IntegerField(default=1)
    meal_day = models.IntegerField(default=1)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.user.username}'s Progress"

    def update_streak(self, activity_date=None):
        """Update streak based on last activity date and the date of the current activity."""
        # Ensure activity_date is a date object
        if activity_date is None:
            # Fallback, though signals should always provide it
            activity_date = timezone.now().date()
        elif isinstance(activity_date, datetime):
            activity_date = activity_date.date()
        elif not isinstance(activity_date, date):
            logger.error(f"Invalid activity_date type ({type(activity_date)}) passed to update_streak for {self.user.id}. Aborting.")
            return

        # Get the most recent *previously recorded* activity date
        last_recorded_activity_date = None
        if self.last_meal_date and self.last_workout_date:
            last_recorded_activity_date = max(self.last_meal_date, self.last_workout_date)
        elif self.last_meal_date:
            last_recorded_activity_date = self.last_meal_date
        elif self.last_workout_date:
            last_recorded_activity_date = self.last_workout_date

        if not last_recorded_activity_date:
            # First ever activity for this user
            if self.current_streak != 1:
                logger.info(f"Starting streak at 1 for {self.user.id} with activity on {activity_date}.")
                self.current_streak = 1
                if 1 > (self.longest_streak or 0):
                    self.longest_streak = 1
                    self.save(update_fields=['current_streak', 'longest_streak'])
                else:
                    self.save(update_fields=['current_streak'])
            return

        # Calculate gap
        gap = activity_date - last_recorded_activity_date

        # --- ADD DEBUGGING PRINTS HERE ---
        print(f"[STREAK DEBUG {self.user.user.username}] Checking streak update:")
        print(f"  - activity_date: {activity_date} (type: {type(activity_date)})")
        print(f"  - last_recorded_activity_date: {last_recorded_activity_date} (type: {type(last_recorded_activity_date)})")
        print(f"  - Calculated gap: {gap} (type: {type(gap)})")
        # Also print the streak value *before* any potential F() expression or reset
        try:
            # Temporarily fetch the current value from DB to show pre-update state
            current_streak_val = UserProgress.objects.get(pk=self.pk).current_streak
            print(f"  - Current streak (from DB before update): {current_streak_val}")
        except UserProgress.DoesNotExist:
             print(f"  - Current streak (instance value before update): {self.current_streak}") # Fallback if not saved yet
        # --- END DEBUGGING PRINTS ---

        if gap == timedelta(days=1):
            logger.info(f"[STREAK DEBUG {self.user.user.username}] Incrementing streak from {self.current_streak}. Gap is 1 day.")
            # Use F expression for atomic increment to avoid race conditions
            self.current_streak = F('current_streak') + 1
            needs_db_save = True
            # Update longest streak if necessary (MOVED LATER)
        elif gap > timedelta(days=1):
            if self.current_streak != 1:
                logger.info(f"Streak broken for {self.user.id}. Resetting streak to 1 for activity on {activity_date}. Gap was {gap.days} days.")
                self.current_streak = 1
                needs_db_save = True
            else:
                logger.debug(f"Activity on {activity_date} is not 1 day after {last_recorded_activity_date}. No streak change. Gap: {gap.days}")
                needs_db_save = False
        elif gap == timedelta(days=0) and (self.current_streak or 0) == 0:
            logger.warning(f"Streak was 0 for {self.user.id} despite activity on {activity_date}. Setting to 1.")
            self.current_streak = 1
            needs_db_save = True # Also need to save if streak was 0 and became 1
        else: # gap <= 0 (and not the case above)
            # No change to streak needed, no save needed for streak itself
            needs_db_save = False
            pass # No change to streak

        # --- Save and Refresh if streak was updated ---
        if needs_db_save:
            self.save(update_fields=['current_streak', 'updated_at'])
            # Refresh to get the actual integer value after F() expression or reset
            self.refresh_from_db(fields=['current_streak'])
            print(f"[STREAK DEBUG {self.user.user.username}] Streak after DB refresh: {self.current_streak}")

            # --- Now update longest_streak AFTER refreshing current_streak ---
            if (self.current_streak or 0) > (self.longest_streak or 0):
                logger.info(f"[STREAK DEBUG {self.user.user.username}] Updating longest_streak from {self.longest_streak} to {self.current_streak}")
                self.longest_streak = self.current_streak
                self.save(update_fields=['longest_streak', 'updated_at'])

class UserScore(models.Model):
    """User's overall fitness score"""
    user = models.OneToOneField(UserProfile, on_delete=models.CASCADE, related_name='score') # Added related_name
    total_score = models.IntegerField(default=0)
    workout_score = models.IntegerField(default=0)
    streak_score = models.IntegerField(default=0)
    nutrition_score = models.IntegerField(default=0)
    goal_score = models.IntegerField(default=0)
    # consecutive_days_active = models.IntegerField(default=0) # Removed - Use UserProgress.longest_streak directly
    total_points_earned = models.IntegerField(default=0)
    perfect_weeks = models.IntegerField(default=0)
    weight_milestones = models.IntegerField(default=0)
    last_calculated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

    def initialize_from_profile(self):
        """Ensure the score object exists. Calculation happens in calculate_score."""
        # This method is primarily called by the signal handler upon user creation.
        # We don't calculate the full score here as the profile might be incomplete.
        # calculate_score will handle the dynamic calculation.
        pass # Just ensures the object is created via get_or_create in the signal.

    def calculate_score(self, calculation_date=None):
        """Calculate the user's overall fitness score, relative to a specific date."""
        # Default to today if no specific date is provided
        if calculation_date is None:
            calculation_date = timezone.now().date()
        elif isinstance(calculation_date, datetime): # Ensure date object
            calculation_date = calculation_date.date()
        elif not isinstance(calculation_date, date):
            logger.error(f"Invalid calculation_date type ({type(calculation_date)}) passed to calculate_score for {self.user.id}. Using today's date.")
            calculation_date = timezone.now().date()

        # Import models inside the method to avoid circular import
        from meals.models import UserMealPlan # Local import
        from workouts.models import WorkoutLog, WorkoutSessionLog, WorkoutSession # Local import

        profile = self.user
        progress, _ = UserProgress.objects.get_or_create(user=profile) # Ensure progress exists

        # --- Calculate workout score (30% of total, max 30 points) ---
        # Based on average completion ratio over the last 7 days ending on calculation_date
        # today = timezone.now().date() # Use calculation_date instead
        start_date = calculation_date - timedelta(days=6)
        relevant_logs = WorkoutLog.objects.filter(
            user=profile,
            date__range=[start_date, calculation_date] # Use calculation_date
        ).prefetch_related('workout_day__sessions', 'session_logs')

        daily_completion_ratios = []
        total_expected_sessions_period = 0
        total_completed_sessions_period = 0

        # Use a set to track processed dates
        processed_dates = set()

        for log in relevant_logs:
            if log.date in processed_dates:
                continue
            processed_dates.add(log.date)
            # Ensure log.workout_day is not None before accessing sessions
            if log.workout_day:
                expected_sessions = log.workout_day.sessions.count()
                if expected_sessions > 0:
                    completed_session_logs_count = log.session_logs.filter(is_completed=True).count()
                    daily_ratio = completed_session_logs_count / expected_sessions
                    daily_completion_ratios.append(daily_ratio)
                    total_expected_sessions_period += expected_sessions
                    total_completed_sessions_period += completed_session_logs_count
            else:
                logger.warning(f"WorkoutLog {log.id} has no associated WorkoutDay during score calculation.")

        # Calculate average completion ratio
        average_ratio = sum(daily_completion_ratios) / len(daily_completion_ratios) if daily_completion_ratios else 0
        self.workout_score = min(30, int(30 * average_ratio))
        # --- End workout score calculation ---

        # Calculate streak score (20% of total, max 20 points)
        # Make it much harder - need 20 day streak for max score (3 weeks consistency)
        current_streak = progress.current_streak or 0
        self.streak_score = min(20, current_streak)  # 1 point per day, need 20 days for max

        # Calculate nutrition score (30% of total, max 30 points)
        # Make it much harder - need 90 meals for max score (3 months of consistency)
        try:
            total_meals = progress.total_meals_completed or 0
            # Each meal is worth 0.33 points, need 90 meals for max 30 points
            self.nutrition_score = min(30, int(total_meals * 0.33))
        except Exception as e:
            logger.error(f"Error calculating nutrition score for user {profile.user.id}: {e}")
            self.nutrition_score = 0

        # Calculate goal score (Profile Completeness Bonus - Max 10 points)
        profile_fields = [
            profile.gender, profile.height, profile.weight, profile.fitness_goal,
            profile.fitness_level, profile.workout_days, profile.health_conditions,
            profile.workout_location, profile.age, profile.is_keto,
            profile.is_intermittent_fasting, profile.cooking_preference,
            profile.water_intake, profile.is_physically_active, profile.has_home_equipment
        ]
        # Count fields that are not None, empty string, or the default 'NONE' for health_conditions
        completed_fields = sum(1 for field in profile_fields if field not in [None, '', ['NONE']])
        self.goal_score = min(10, int((completed_fields / max(len(profile_fields), 1)) * 10)) # Max 10 points

        # Note: consecutive_days_active was removed. Use progress.longest_streak directly if needed elsewhere.

        # --- Realistic Initial Score Logic ---
        # Start with a much lower baseline score
        baseline_score = 20  # Reduced from 40
        questionnaire_bonus = 0

        # Add smaller points based on fitness level
        if profile.fitness_level == 'INTERMEDIATE':
            questionnaire_bonus += 3  # Reduced from 5
        elif profile.fitness_level == 'ADVANCED':
            questionnaire_bonus += 5  # Reduced from 10

        # Add smaller points based on physical activity
        if profile.is_physically_active:
            questionnaire_bonus += 2  # Reduced from 5

        # Add smaller points based on workout days
        if profile.workout_days == '4_5_DAYS':
            questionnaire_bonus += 2  # Reduced from 5
        elif profile.workout_days == '6_7_DAYS':
            questionnaire_bonus += 3  # Reduced from 10

        # Add profile completeness bonus (much smaller)
        profile_completeness_bonus = min(5, self.goal_score // 2)  # Max 5 points instead of 10
        questionnaire_bonus += profile_completeness_bonus

        # Calculate initial score based on baseline + questionnaire factors (max ~35-40)
        initial_score = min(40, baseline_score + questionnaire_bonus)  # Much lower cap

        # --- Combine Initial Score with Activity Scores ---
        # Activity scores (workout, nutrition, streak) are calculated above
        # Total possible: 40 (initial) + 30 (workout) + 30 (nutrition) + 20 (streak) = 120, capped at 100
        self.total_score = min(100, initial_score + int(self.workout_score or 0) + int(self.streak_score or 0) + int(self.nutrition_score or 0))

        # Keep goal_score separate if it represents only profile completeness
        # Or adjust its calculation/meaning if needed. For now, it contributes to initial score.
        # self.goal_score remains as calculated before.

        # Update weight milestones (ensure values are numeric)
        if profile.weight and profile.initial_weight and profile.fitness_goal == 'LOSE_WEIGHT':
            try:
                weight_lost = float(profile.initial_weight) - float(profile.weight)
                new_milestones = int(weight_lost // 2) # Milestones for every 2 units lost
                if new_milestones > self.weight_milestones:
                    # Add points only for new milestones achieved
                    self.total_points_earned += (new_milestones - self.weight_milestones) * 10
                    self.weight_milestones = new_milestones
            except (ValueError, TypeError) as e:
                logger.error(f"Error calculating weight milestones for user {profile.user.id}: {e}")

        self.save()
        logger.info(f"Score calculation completed for profile {profile.id} on {calculation_date}. Total Score: {self.total_score}, Workout: {self.workout_score}, Nutrition: {self.nutrition_score}, Streak: {self.streak_score}")

        # --- Call DailyScore Snapshot --- (Ensure this is called if needed)
        # Pass calculation_date to snapshot?
        # try:
        #     DailyScore.create_snapshot(user_profile=profile, score_date=calculation_date)
        #     logger.info(f"Created DailyScore snapshot for {profile.user.username} on {calculation_date}")
        # except Exception as e:
        #     logger.error(f"Error creating DailyScore snapshot: {e}", exc_info=True)
        # --- End Snapshot Call ---

    def __str__(self):
        return f"{self.user}'s score"

class DailyScore(models.Model):
    """Track user's daily fitness score (Snapshot of UserScore)""" # Updated docstring
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='daily_scores') # Added related_name
    date = models.DateField()
    total_score = models.IntegerField(default=0)
    workout_score = models.IntegerField(default=0)
    streak_score = models.IntegerField(default=0)
    nutrition_score = models.IntegerField(default=0)
    goal_score = models.IntegerField(default=0)
    points_earned = models.IntegerField(default=0) # Keep for daily points? Or remove?
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('user', 'date')
        ordering = ['-date']

    def __str__(self):
        return f"{self.user.user.username}'s score on {self.date}"

    # Removed calculate_daily_score method

    @classmethod
    def create_snapshot(cls, user_profile, score_date):
        """Creates or updates a DailyScore entry based on the current UserScore."""
        user_score, _ = UserScore.objects.get_or_create(user=user_profile)
        # Ensure the main score is up-to-date before taking snapshot
        user_score.calculate_score()

        # Calculate points earned *today* (optional, based on logs for score_date)
        # This logic might be better placed elsewhere or simplified
        points_earned_today = 0
        try:
            from workouts.models import WorkoutLog
            from meals.models import MealLog
            workouts_completed = WorkoutLog.objects.filter(user=user_profile, date__date=score_date, is_completed=True).count()
            meals_completed = MealLog.objects.filter(user=user_profile, date__date=score_date, is_completed=True).count()
            points_earned_today = (workouts_completed * 10) + (meals_completed * 5)
            if workouts_completed > 0: points_earned_today += 5 # Bonus
            if meals_completed >= 3: points_earned_today += 5 # Bonus
        except Exception as e:
            logger.error(f"Error calculating daily points earned for {user_profile.id} on {score_date}: {e}")


        daily_score, created = cls.objects.update_or_create(
            user=user_profile,
            date=score_date,
            defaults={
                'total_score': user_score.total_score,
                'workout_score': user_score.workout_score,
                'streak_score': user_score.streak_score,
                'nutrition_score': user_score.nutrition_score,
                'goal_score': user_score.goal_score,
                'points_earned': points_earned_today, # Store daily points earned
                # Add other relevant fields from UserScore if needed
            }
        )
        logger.info(f"DailyScore {'created' if created else 'updated'} for {user_profile.id} on {score_date}. Score: {daily_score.total_score}")
        return daily_score
