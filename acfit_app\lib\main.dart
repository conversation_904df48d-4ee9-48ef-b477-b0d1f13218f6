import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'generated/l10n.dart';
import 'services/local_notification_service.dart';
import 'services/background_notification_service.dart';
import 'providers/auth_provider.dart';
import 'providers/language_provider.dart';
import 'services/notification_service.dart'; // Import NotificationService
import 'utils/logger.dart'; // Import Logger and LogConfig
import 'config/api_config.dart'; // Import API config for base URL
import 'services/navigation_service.dart';
import 'providers/workout_provider.dart';
import 'providers/fitness_provider.dart';
import 'providers/user_progress_provider.dart';
import 'providers/meal_provider.dart';
import 'services/api_service.dart';
import 'services/auth_service.dart';
import 'theme/app_theme.dart';

// Custom page transitions builder for instant transitions
class _InstantPageTransitionsBuilder extends PageTransitionsBuilder {
  const _InstantPageTransitionsBuilder();

  @override
  Widget buildTransitions<T extends Object?>(
    PageRoute<T> route,
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return child; // No animation, just return the child directly
  }
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // --- Initialize Logger with focused logging ---
  Logger.setLoggingEnabled(true); // Enable logging
  LogConfig.disableAllLogs(); // First disable all logs
  LogConfig.enableMealLogs = false; // Enable meal logs for debugging
  LogConfig.enableMealScreenLogs = false; // Enable meal screen logs
  // Only enable workout and error logs
  LogConfig.enableWorkoutLogs = false;
  LogConfig.enableWorkoutScreenLogs = false;
  LogConfig.enableErrorLogs = false; // Enable error logs

  // Only enable API logs for workout completion
  LogConfig.enableApiLogs = true; // Enable API logs
  LogConfig.enableTempLogs = true; // Enable temporary logs

  // Exclude all tags except those related to workout completion
  LogConfig.clearExcludedTags();
  LogConfig.excludeTag('DioLog'); // Exclude verbose Dio logs

  Logger.log("Starting AC Fit App with FOCUSED logs enabled",
      tag: "AppInit", force: true);
  Logger.workout("Workout logging is ENABLED", tag: "AppInit", force: true);
  Logger.log("API Base URL: $apiBaseUrl", tag: "AppInit", force: true);
  // ---------------------------------------------

  // --- Initialize Timezone Database ---
  tz.initializeTimeZones();
  // ------------------------------------

  // --- Initialize Local Notifications ---
  await LocalNotificationService.initialize();
  await LocalNotificationService.requestPermissions();
  // --------------------------------------

  // --- Initialize Background Notification Service ---
  await BackgroundNotificationService.initialize();
  await BackgroundNotificationService.scheduleNotificationSync();
  await BackgroundNotificationService.scheduleHydrationReminders();
  // --------------------------------------------------

  // Initialize services
  const storage = FlutterSecureStorage();
  final apiService = ApiService();
  final authService = AuthService();

  // --- Create Providers ---
  final authProvider = AuthProvider(authService); // Create AuthProvider here
  final languageProvider = LanguageProvider(); // Create LanguageProvider
  await languageProvider.init(); // Initialize language settings
  // -------------------------

  // Set system UI overlay style with orange status bar
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Color(0xFFF97316), // Match the orange color
      statusBarIconBrightness:
          Brightness.light, // White icons for orange background
      systemNavigationBarColor: Color(0xFFF97316), // Orange nav bar too
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(
    MyApp(
      storage: storage,
      apiService: apiService,
      authService: authService,
      authProvider: authProvider, // Pass AuthProvider instance
      languageProvider: languageProvider, // Pass LanguageProvider instance
    ),
  );
}

class MyApp extends StatefulWidget {
  final FlutterSecureStorage storage;
  final ApiService apiService;
  final AuthService authService;
  final AuthProvider authProvider; // Add AuthProvider field
  final LanguageProvider languageProvider; // Add LanguageProvider field

  const MyApp({
    Key? key,
    required this.storage,
    required this.apiService,
    required this.authService,
    required this.authProvider, // Add to constructor
    required this.languageProvider, // Add to constructor
  }) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Provide the existing authService instance
    return MultiProvider(
      providers: [
        // Provide ApiService instance created in main()
        Provider<ApiService>.value(value: widget.apiService),
        // Provide AuthService instance using ChangeNotifierProvider.value
        // because AuthService uses the ChangeNotifier mixin.
        ChangeNotifierProvider<AuthService>.value(value: widget.authService),
        // Provide AuthProvider instance using .value
        ChangeNotifierProvider<AuthProvider>.value(value: widget.authProvider),
        // Provide LanguageProvider instance using .value
        ChangeNotifierProvider<LanguageProvider>.value(
            value: widget.languageProvider),
        // Use ChangeNotifierProxyProvider for WorkoutProvider which depends on AuthProvider
        ChangeNotifierProxyProvider<AuthProvider, WorkoutProvider>(
          // Create receives the single AuthProvider instance via context now
          create: (context) => WorkoutProvider(
            apiService: widget.apiService,
            authProvider:
                widget.authProvider, // Use the passed instance directly
          ),
          // Update receives the correct AuthProvider instance from the provider above
          update: (context, authProvider, previousWorkoutProvider) =>
              WorkoutProvider(
            apiService: widget.apiService, // Reuse the service
            authProvider: authProvider, // Use the updated/correct AuthProvider
          ),
        ),
        ChangeNotifierProvider<FitnessProvider>(
          create: (_) => FitnessProvider(apiService: widget.apiService),
        ),
        ChangeNotifierProvider<UserProgressProvider>(
          create: (_) => UserProgressProvider(),
        ),
        // Add MealProvider which depends on AuthProvider and UserProgressProvider
        ChangeNotifierProxyProvider2<AuthProvider, UserProgressProvider,
            MealProvider>(
          create: (context) => MealProvider(
            apiService: widget.apiService,
            authProvider: widget.authProvider,
            userProgressProvider:
                Provider.of<UserProgressProvider>(context, listen: false),
          ),
          update: (context, authProvider, userProgressProvider,
                  previousMealProvider) =>
              MealProvider(
            apiService: widget.apiService,
            authProvider: authProvider,
            userProgressProvider: userProgressProvider,
          ),
        ),
      ],
      child: Consumer<LanguageProvider>(
        builder: (context, languageProvider, _) {
          return MaterialApp(
            title: 'AC Fit App',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.theme.copyWith(
              pageTransitionsTheme: const PageTransitionsTheme(
                builders: {
                  TargetPlatform.android: _InstantPageTransitionsBuilder(),
                  TargetPlatform.iOS: _InstantPageTransitionsBuilder(),
                },
              ),
            ),
            darkTheme: AppTheme.theme, // Use light theme for dark mode too
            themeMode: ThemeMode.light, // Force light mode
            navigatorKey: NavigationService.navigatorKey,
            onGenerateRoute: NavigationService.generateRoute,
            initialRoute: NavigationService.initial,
            // Add localization support
            locale: languageProvider.locale,
            supportedLocales: AppLocalizations.supportedLocales,
            localizationsDelegates: AppLocalizations.localizationsDelegates,
          );
        },
      ),
    );
  }
}
