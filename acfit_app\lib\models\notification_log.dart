class NotificationLog {
  final String id;
  final String notificationType;
  final String title;
  final String message;
  final String status;
  final DateTime? sentAt;
  final DateTime? readAt;
  final DateTime createdAt;
  final Map<String, dynamic> metadata;

  NotificationLog({
    required this.id,
    required this.notificationType,
    required this.title,
    required this.message,
    required this.status,
    this.sentAt,
    this.readAt,
    required this.createdAt,
    required this.metadata,
  });

  factory NotificationLog.fromJson(Map<String, dynamic> json) {
    return NotificationLog(
      id: json['id'] as String,
      notificationType: json['notification_type'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      status: json['status'] as String,
      sentAt: json['sent_at'] != null ? DateTime.parse(json['sent_at']) : null,
      readAt: json['read_at'] != null ? DateTime.parse(json['read_at']) : null,
      createdAt: DateTime.parse(json['created_at']),
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'notification_type': notificationType,
      'title': title,
      'message': message,
      'status': status,
      'sent_at': sentAt?.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  bool get isRead => status == 'READ';
  bool get isUnread => status == 'SENT' || status == 'PENDING';

  String get displayType {
    switch (notificationType) {
      case 'WORKOUT_REMINDER':
        return 'Workout Reminder';
      case 'MEAL_REMINDER':
        return 'Meal Reminder';
      case 'HYDRATION_REMINDER':
        return 'Hydration Reminder';
      case 'WEEKLY_MOTIVATION':
        return 'Weekly Motivation';
      case 'ADMIN_MESSAGE':
        return 'Message';
      default:
        return 'Notification';
    }
  }

  String get icon {
    switch (notificationType) {
      case 'WORKOUT_REMINDER':
        return '💪';
      case 'MEAL_REMINDER':
        return '🍽️';
      case 'HYDRATION_REMINDER':
        return '💧';
      case 'WEEKLY_MOTIVATION':
        return '🌟';
      case 'ADMIN_MESSAGE':
        return '📢';
      default:
        return '🔔';
    }
  }
}
