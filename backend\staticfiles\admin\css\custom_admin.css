/* Custom Admin Styles */

/* Fix for MultiSelectField display */
.field-health_conditions_display,
.field-health_conditions,
.field-health_conditions_allowed_display,
.field-health_conditions_allowed,
.multiselectfield,
.vTextField,
.readonly,
[class*='field-'][class*='_display'],
[class*='field-'][class*='conditions'],
.select2-selection__rendered,
.select2-selection--multiple,
.select2-selection--single,
.select2-container,
.select2-results__option,
.select2-results__options,
.select2-results,
.select2-selection,
.select2-selection__choice,
.select2-selection__choice__remove,
.select2-selection__placeholder,
.select2-search--inline,
.select2-search__field {
    /* Ensure text is fully visible */
    line-height: 1.5 !important;
    height: auto !important;
    min-height: 20px !important;
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    word-wrap: break-word !important;
    font-size: 14px !important;
    max-height: none !important;
}

/* Apply to all form fields to be safe */
.form-row input,
.form-row select:not(.selector-available select):not(.selector-chosen select),
.form-row textarea,
.form-row .readonly,
.form-row p,
.form-row span,
.form-row div:not(.selector):not(.selector-available):not(.selector-chosen),
.form-row label {
    line-height: 1.5 !important;
    height: auto !important;
    min-height: 20px !important;
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    word-wrap: break-word !important;
}

/* Apply to table cells in list views */
td, th {
    line-height: 1.5 !important;
    height: auto !important;
    min-height: 20px !important;
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    word-wrap: break-word !important;
    padding: 8px !important;
}

/* Fix for checkboxes in MultiSelectField */
.checkbox-row {
    display: block !important;
    margin-bottom: 5px !important;
}

/* Make sure the text is visible in all contexts */
.field-health_conditions_display *,
.field-health_conditions *,
.field-health_conditions_allowed_display *,
.field-health_conditions_allowed *,
.multiselect-value,
[class*='field-'][class*='_display'] *,
[class*='field-'][class*='conditions'] *,
.select2-selection__rendered *,
.select2-selection--multiple *,
.select2-selection--single *,
.select2-container *,
.select2-results__option *,
.select2-results__options *,
.select2-results *,
.select2-selection *,
.select2-selection__choice *,
.select2-selection__choice__remove *,
.select2-selection__placeholder *,
.select2-search--inline *,
.select2-search__field * {
    text-rendering: optimizeLegibility !important;
    max-height: none !important;
    line-height: 1.5 !important;
    display: inline-block !important;
    padding: 2px 4px !important;
    margin: 1px !important;
    border-radius: 3px !important;
    background-color: #f8f5ff !important; /* Light purple background */
    font-size: 14px !important;
    font-weight: normal !important;
    color: #333 !important;
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    word-wrap: break-word !important;
}

/* Additional fixes for MultiSelectField display in Select2 dropdowns */
.select2-container--default .select2-selection--single .select2-selection__rendered,
.select2-container--default .select2-selection--multiple .select2-selection__rendered,
.select2-container--default .select2-results > .select2-results__options,
.select2-container--default .select2-results__option,
.select2-container--default .select2-selection--multiple .select2-selection__choice,
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    word-wrap: break-word !important;
    max-height: none !important;
}

/* Fix for MultiSelectField in list view */
td[class*='field-'][class*='conditions'],
td[class*='field-'][class*='_display'] {
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    word-wrap: break-word !important;
    max-height: none !important;
    line-height: 1.5 !important;
}

/* Override any inline styles that might be causing truncation */
[style*='overflow:hidden'],
[style*='overflow: hidden'],
[style*='text-overflow:ellipsis'],
[style*='text-overflow: ellipsis'],
[style*='white-space:nowrap'],
[style*='white-space: nowrap'] {
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
}

/* Style for the multiselect values container */
.multiselect-values-container {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 4px !important;
    width: 100% !important;
    max-width: 100% !important;
    overflow: visible !important;
    padding: 4px !important;
}

/* Style for individual multiselect values */
.multiselect-value {
    display: inline-block !important;
    padding: 4px 8px !important;
    margin: 2px !important;
    border-radius: 4px !important;
    background-color: #f0e6ff !important; /* Lighter purple */
    border: 1px solid #d9c6ff !important; /* Faded purple border */
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: #333 !important;
    max-width: none !important;
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    word-wrap: break-word !important;
}

/* Style for error values */
.error-value {
    display: inline-block !important;
    padding: 4px 8px !important;
    margin: 2px !important;
    border-radius: 4px !important;
    background-color: #fee2e2 !important;
    border: 1px solid #ef4444 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: #b91c1c !important;
    max-width: none !important;
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    word-wrap: break-word !important;
}

/* Fix for Select2 dropdown */
.select2-container--open .select2-dropdown {
    max-height: none !important;
    overflow: visible !important;
}

.select2-container--open .select2-dropdown .select2-results {
    max-height: none !important;
    overflow: visible !important;
}

.select2-container--open .select2-dropdown .select2-results .select2-results__options {
    max-height: 300px !important; /* Allow scrolling but with a reasonable height */
    overflow-y: auto !important;
    overflow-x: visible !important;
}

.select2-container--open .select2-dropdown .select2-results .select2-results__options .select2-results__option {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow: visible !important;
    text-overflow: clip !important;
    padding: 6px 8px !important;
    line-height: 1.5 !important;
}

/* Dashboard links */
.dashboard-link,
.users-link,
.workout-plans-link,
.meal-plans-link,
.user-workout-plans-link,
.user-meal-plans-link,
.assign-plans-link,
.questionnaire-link,
.products-link,
.videos-link,
.notifications-link,
.documentation-link,
.activity-logs-link {
  display: inline-block;
  padding: 8px 12px;
  margin-right: 10px;
  background-color: #6b46c1; /* Medium purple */
  color: white !important;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  transition: background-color 0.2s;
}

.dashboard-link:hover,
.users-link:hover,
.workout-plans-link:hover,
.meal-plans-link:hover,
.user-workout-plans-link:hover,
.user-meal-plans-link:hover,
.assign-plans-link:hover,
.questionnaire-link:hover,
.products-link:hover,
.videos-link:hover,
.notifications-link:hover,
.documentation-link:hover,
.activity-logs-link:hover {
  background-color: #553c9a; /* Darker purple */
  color: white !important;
}

/* Header styling */
#header {
  background: #1a1625; /* Dark purple-black */
  color: white;
}

#branding h1 {
  color: white;
}

#branding h1 a {
  color: white;
}

/* Module headers */
.module h2 {
  background: #6b46c1; /* Medium purple */
  color: white;
}

/* User tools navigation */
#user-tools {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0;
}

#user-tools li {
  display: inline-block;
  margin-right: 5px;
  margin-bottom: 5px;
}

/* Dashboard stats */
.dashboard-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.stat-card {
  background-color: #f8f5ff; /* Light purple background */
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(107, 70, 193, 0.1); /* Purple shadow */
  padding: 20px;
  flex: 1;
  min-width: 200px;
}

.stat-card h3 {
  font-size: 16px;
  color: #1a1625; /* Dark purple-black */
}

.stat-card .stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #6b46c1; /* Medium purple */
}
